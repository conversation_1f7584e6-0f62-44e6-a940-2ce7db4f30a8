var Sequelize = require('sequelize')
var env = require('../config/environment')

var userAdsAnalytic = sequelize.define(
    'user_ads_analytic',
    {
        id: {
            type: Sequelize.INTEGER,
            autoIncrement: true,
            primaryKey: true
        },
        adsID: {
            type: Sequelize.INTEGER,
            allowNull: false
        },
        userID: {
            type: Sequelize.INTEGER,
            allowNull: false,
        },
        impressions: {
            type: Sequelize.INTEGER,
            defaultValue: 0,
        },
        clicks: {
            type: Sequelize.INTEGER,
            defaultValue: 0,
        },
        last_impression_at: {
            type: Sequelize.DATE,
            allowNull: true,
        },
        last_click_at: {
            type: Sequelize.DATE,
            allowNull: true,
        }
    },
    {
        freezeTableName: true,
        timestamps: true,
        updatedAt: 'updated_at',
        createdAt: 'created_at',
    }
)
module.exports = userAdsAnalytic