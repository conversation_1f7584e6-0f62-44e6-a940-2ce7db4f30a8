CREATE TABLE IF NOT EXISTS `user_ads_save` (
  `id` int NOT NULL AUTO_INCREMENT,
  `adsID` int NOT NULL,
  `userID` int NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `user_ads_analytic` (
  `id` int NOT NULL AUTO_INCREMENT,
  `adsID` int NOT NULL,
  `userID` int NOT NULL,
  `impressions` int NOT NULL DEFAULT '0',
  `clicks` int NOT NULL DEFAULT '0',
  `last_impression_at` date DEFAULT NULL,
  `last_click_at` date DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PR<PERSON>AR<PERSON>EY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

ALTER TABLE `orders` ADD `totalDiscountedAmount` FLOAT NULL DEFAULT NULL AFTER `docketPrintingStatus`;
