var Sequelize = require('sequelize')
const Op = Sequelize.Op
var multer = require('multer')

const message = require('../config/message')
const common = require('./common')
const commonFunction = require('../common/commonFunction')

const order = require('../models/orders')
const orderItems = require('../models/orderItems')
const orderItemWaitTimeNotifications = require('../models/orderItemWaitTimeNotifications')
const orderItemExtras = require('../models/orderItemExtras')
const orderItemVariants = require('../models/orderItemVariants')
const coupons = require('../models/coupons')
const couponsSubCatIds = require('../models/couponsSubCatIds')// 9. Promo codes - Subheading specific....
const pickupCodeModel = require('../models/pickupCode')
const transLogsModel = require('../models/transactionLogs')
const transErrorLogsModel = require('../models/transactionErrorLogs')
const cartItems = require('../models/cartItems')
const cartProductVariantTypes = require('../models/cartProductVariantTypes')
const cartProductVariantSubTypes = require('../models/cartProductVariantSubTypes')
const orderProductVariantTypes = require('../models/orderProductVariantTypes')
const orderProductVariantSubTypes = require('../models/orderProductVariantSubTypes')
const productVariantTypes = require('../models/productVariantTypes')
const productVariantSubTypes = require('../models/productVariantSubTypes')

const user = require('../models/user')
const userConsentVenue = require('../models/userConsentVenue')
const bar = require('../models/bar')
const barSubCategoryOpeningHoursUTC = require('../models/barSubCategoryOpeningHoursUTC')
const product = require('../models/product')
const productVariants = require('../models/productVariants')
const productExtras = require('../models/productExtras')
const pickupLocation = require('../models/pickupLocation')
const subCategory = require('../models/subCategory')
const settings = require('../models/settings')
const barNotification = require('../models/barNotification')
const userNotification = require('../models/userNotification')
const docketNotification = require('../models/docketNotification')
const orderTax = require('../models/orderTax')
const orderRefundTax = require('../models/orderRefundTax')
const itemActiveHours = require('../models/itemActiveHours')
const sequelize = require('sequelize')

var env = require('../config/environment')
var jwt = require('jsonwebtoken')
const moment = require('moment')
const categoryModel = require('../models/category')
const segmentProductTags = require('../models/segmentProductTags')
const userBarTags = require('../models/userBarTags')
const orderTableNumber = require('../models/orderTableNumber')
const operatingHours = require("../models/operatingHours");
const POSconfig = require('../models/POSconfig')
const productModel = require("../models/product");
const subcategory = require("../models/subCategory");
const barAccessToken = require('../models/barAccessToken')
var stripe = require('stripe')(env.stripe_secret_key)
var CronJob = require('cron').CronJob;
const taxModel = require('../models/tax')

var AWS = require('aws-sdk')
var multerS3 = require('multer-s3')

// Set AWS SDK configuration
AWS.config.update({
  secretAccessKey: env.awsSecretAccessKey,
  accessKeyId: env.awsAccessKey,
  region: env.awsRegion
})


const chargeVenuePerMonth = async () => {
  // List of all accounts from stripe....
  const accounts = await stripe.accounts.list({ limit: 1000 });
  const stripeRetrievedIds = accounts.data.filter(stripeAccount => stripeAccount.charges_enabled && stripeAccount.payouts_enabled)

  // Stripe-Accounts which are connected in db....
  await bar.findAll({
    attributes: [
      'id', 'stripeID'
    ],
    where: {
      [Op.and]: [
        { stripeID: stripeRetrievedIds.map(stripeId => stripeId.id) },
        { stripeID: { [Op.ne]: null } },
        { stripeID: { [Op.ne]: "" } },
      ]
    },
  }).then(async barDetails => {
    for (const barDetail of barDetails) {
      try {
        await stripe.charges.create({
          amount: 2 * 100,
          currency: "aud",
          source: barDetail.stripeID,
          description: "Monthly Stripe $2 Fee Deduction",
          statement_descriptor_suffix: "MyTab Stripe Fee" // 4. Refund bank description
        }, (err, charge) => {
          console.log(err, charge)
        })
      } catch (e) {
        console.log(e)
      }
    }
  }
  )
}
// Cron Job for charging every month to venue....
// new CronJob('0 0 25 * *', chargeVenuePerMonth, null, true, 'Australia/Perth');

async function createorderrandomnumber(barData) {
  var min = 1000000
  var max = 9999999
  var randomNum = Math.floor(Math.random() * (max - min + 1)) + min

  if (barData && barData.restaurantName) {
    var orderNo = barData.restaurantName.slice(0, 3).toUpperCase() + randomNum
  } else {
    var orderNo = randomNum
  }

  const orderData = await order.findAll({
    attributes: ['orderNo'],
    where: {
      orderNo: orderNo
    }
  })
  
  if (orderData.length > 0) {
    return createorderrandomnumber(barData)
  } else {
    return orderNo
  }
}

async function createPickupCode(barData) {
  const pickupCodeData = await pickupCodeModel.findOne({
    where: {
      pickupcode: { [Op.notIn]: Sequelize.literal(`(select pickupCode from orders where barID = ${barData.id} and orderServiceType = 'PICKUP' and orderDate >= DATE_ADD(CURDATE(), INTERVAL -15 DAY))`) }
    },
    order: Sequelize.literal('rand()'),
  })

  return pickupCodeData ? pickupCodeData.pickupcode.toUpperCase() : 'ROSE'
}

function groupBy(xs, key) {
  return xs.reduce(function (rv, x) {
    (rv[x[key]] = rv[x[key]] || []).push(x);
    return rv;
  }, {});
}

function groupByOrderItems(orderItems, orderServiceType = 'PICKUP', refundStatus = 'no') {
  let order_items = JSON.parse(JSON.stringify(orderItems))
  // Group By Sub Category ID
  // let groupByArr = groupBy(order_items, 'subCategoryID');
  // let newOrderItemsArr = [];
  // Object.entries(groupByArr).forEach(([key, value]) => {
    let newValueItemsArr = [];
    if(orderServiceType === 'PICKUP'){
      // Group By Pickup Location
      let groupByPickupLocationArr = groupBy(order_items, 'pickupLocation');
      Object.entries(groupByPickupLocationArr).forEach(([key, value]) => {
        // Group By Wait Time
        let groupByWaitTimeArr = groupBy(value, 'waitTime');
        let newValueItemsArr1 = [];
        Object.entries(groupByWaitTimeArr).forEach(([key, value]) => {
          newValueItemsArr1.push({ wait_time: value[0] && value[0].expectedTime, orderStatus: value[0].orderStatus, refundStatus: refundStatus, items: value });
        });
  
        let pickup_location = value[0] && value[0].pickupLocation;
        Object.entries(newValueItemsArr1).forEach(([key, value]) => {
          newValueItemsArr.push({ pickup_location: pickup_location, ...value });
        });
      });
    }else{
       // Group By Wait Time
       let groupByWaitTimeArr = groupBy(order_items, 'waitTime');
       Object.entries(groupByWaitTimeArr).forEach(([key, value]) => {
         newValueItemsArr.push({ wait_time: value[0] && value[0].expectedTime, orderStatus: value[0].orderStatus, refundStatus: refundStatus, items: value });
       });
    }
    newValueItemsArr.sort(function(a,b){
      return new Date(a.wait_time) - new Date(b.wait_time);
    });
    // let subCategoryID = value[0]?.subCategoryID;
    // Object.entries(newValueItemsArr).forEach(([key, value]) => {
    //   newOrderItemsArr.push({ subCategoryID: subCategoryID, ...value });
    // });
  // });

  return newValueItemsArr
}

async function pushNotificationForOrdersItems(orderDetails, status) {
  let orderID = orderDetails.dataValues && orderDetails.dataValues.orderID
  let orderServiceType = orderDetails.dataValues && orderDetails.dataValues.orderServiceType
  let pickupCode = orderDetails.dataValues && orderDetails.dataValues.pickupCode
  let userID = orderDetails.dataValues && orderDetails.dataValues.userID
  let barID = orderDetails.dataValues && orderDetails.dataValues.barID

  let notification_type, posPayload, message;
  // Table code seperated for new change
  let table_code = await orderTableNumber.findOne({
    attributes: ['tableCode'],
    where: {orderID: orderID},
    order: [['id', 'DESC']]
  })
  const tableCode = table_code && table_code.tableCode

  switch (status) {
    case 'Preparing':
      message = orderServiceType === 'PICKUP' ? `Your order ${pickupCode} is confirmed and being prepared.` : `Order for table #${tableCode} is confirmed and being prepared.`;
      notification_type = 'orderPreparing'
      posPayload = {};
      break;

    case 'Pickup':
      message = orderServiceType === 'PICKUP' ? `Your order ${pickupCode} is ready to collect.` : `Your order for table #${tableCode} is ready.`;
      notification_type = 'orderReady';
      posPayload = {};
      break;

    case 'Pickedup':
      message = orderServiceType === 'PICKUP' ? `Thank you for collecting your order ${pickupCode}. Enjoy!` : `Your order has been delivered to your table #${tableCode}. Enjoy!`;
      notification_type = 'orderPickedup'
      posPayload = {};
      break;

    case 'NotPickedup':
      message = orderServiceType === 'PICKUP' ? `Your order ${pickupCode} has been marked as not collected. Please contact the venue.` : `Your order for table #${tableCode} has been marked as not collected. Please contact the venue.`;
      notification_type = 'orderNotPickedup'
      break;
  }

  if (message !== '') {
    await userNotification.create({
      barID: barID,
      notification_type: notification_type,
      userID: userID,
      dataID: orderID,
      message: message,
      createdAt: new Date(),
      updatedAt: new Date()
    })
    await commonFunction.orderStatusNotificationToUser(userID, orderID, notification_type, message)
  }

}

exports.create = async (req, res, next) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var userID = sessionData.userID
  try {

    if (req.body.transactionFee == 0) {
      return res.status(200).json({
        success: 2,
        message: "We do apologise, to complete your order you will need to update your MyTab app to the newest version. Thank You!",
      })
    }

    const intoxicatedOrderData = await order.findOne({
      attributes: ['id', 'intoxicatedDate'],
      where: {
        userID: userID,
        barID: req.body.barID,
        orderStatus: 'Intoxicated'
      },
      order: [['id', 'DESC']],
    })
    
    if (intoxicatedOrderData) {
      let intoxicatedDate = moment(new Date(intoxicatedOrderData.intoxicatedDate)).add(1, 'days').format('YYYY-MM-DD 04:00:00')
      let today = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
      if (today < intoxicatedDate) {
        res.status(200).json({
          success: 0,
          message: "Your safety is our priority, this venue has listed you as too intoxicated and is refusing you service for remainder of tonight.",
        })
        return false;
      }
    }

    if (req.body.isConfirm && req.body.isConfirm == 'false') {
      const prevOrderData = await order
        .findOne({
          where: {
            isDeleted: 'No',
            isCanceled: 'No',
            paymentStatus : 'received',
            userID: userID,
            barID: {
              $ne: req.body.barID
            },
            [Op.or]: [
              {
                orderStatus: 'New'
              },
              {
                orderStatus: 'Preparing'
              },
              {
                orderStatus: 'Pickup'
              }
            ]
          },
          attributes: [
            'id',
            'orderNo',
            // [Sequelize.literal(`IF(orderServiceType='TABLE', tableCode, pickupCode)`), 'pickupCode'],
            'subTotal',
            'transactionFee',
            'tax',
            'total',
            'userID',
            'barID',
          ],
          // include: [
          //   {
          //     required: true,
          //     model: bar,
          //     attributes: ['id', 'restaurantName']
          //   }
          // ],
          order: [['createdAt', 'DESC'], ['orderStatus', 'DESC']],
          distinct: true,
          duplicating: false
        })
      if (prevOrderData) {
        res.status(200).json({
          success: 2,
          message: "Your orders preparing at another venue, Please confirm you want to continue this order.",
        })
        return false;
      }
    }

    const barData = await bar.findOne({
      attributes: [
        'id', 'restaurantName', 'managerName', 'email', 'countryCode', 'mobile', 'stripeID', 'venueId', 'posStatus', 'attachedPosConfig', 'docketCommission', 'docketStatus', 'posFee', 'serviceType', 'waitTimeServiceType', 'pauseOrderStartTime', 'pauseOrderLimit'
      ],
      where: {
        id: req.body.barID
      },
      // include: [POSconfig]
    })

    if(barData?.dataValues?.pauseOrderStartTime){
      let endTime = moment.utc(barData.dataValues.pauseOrderStartTime).add(barData.dataValues.pauseOrderLimit,'minutes');
      if(endTime.isAfter(moment.utc())){
        return res.status(200).send({
          success: -6,
          message: 'Due to high order volume, new orders are temporarily paused'
        })
      }
    }
    
    // const isPOSConfigured = barData.attachedPosConfig && barData.attachedPosConfig !== ''
    const userData = await user.findOne({
      attributes: ['id', 'fullName', 'email', 'countryCode', 'mobile', 'stripeID'],
      where: {
        id: userID
      }
    })

    var categoryData = await categoryModel.findOne({
      where: { name: 'kitchen', isDeleted: 'No' },
      attributes: ['id'],
      raw: true
    });
    if (!categoryData) {
      return res.status(200).json({ success: 0, message: 'Category not found.', data: {} });
    }
    if (req.body.paymentType && req.body.paymentType == 1) {
      if (req.body.cardid && req.body.cardid != '') {
        const card = await stripe.customers.retrieveSource(
          userData.stripeID,
          req.body.cardid
        ).catch(err => err);
        if (card.statusCode === 404) {
          return res.status(200).json({
            success: 0,
            message: 'Provided cards detail are not linked with this user.',
            data: {}
          });
        }
      } else {
        await res.status(200).send({
          success: 0,
          message: 'Payment failed! Please make sure you have added a payment account'
        })
      }
    }

    let pickupCode
    let serviceType
    let tableFlag = false;
    let productIDs=[]
    const randomNum = await createorderrandomnumber(barData)
    if (req.body.order_service_type && req.body.order_service_type.toLowerCase() === 'table') {
      if (!req.body.table_code || req.body.table_code && req.body.table_code === '')
        return res.status(200).json({
          success: 0,
          message: 'Table number is required on table service venues!',
          data: {}
        });
      else {
        tableFlag = true
        serviceType = 'TABLE'
      }
    } else {
      pickupCode = await createPickupCode(barData)
      serviceType = 'PICKUP'
    }

    const requestOrderItems = JSON.parse(req.body.orderItems);
   
    for (const requestOrderItem of requestOrderItems) {
      productIDs.push(requestOrderItem?.productID)
      let productDetail = await commonFunction.getProductDetail(requestOrderItem['productID']);
      requestOrderItem.productDetail = productDetail
      if(productDetail.isStockLimit == 'Yes'){
        if(productDetail.stock == 0 || productDetail.stock < requestOrderItem['quantity']){
          return res.status(200).json({
            success: 0,
            message: 'Your cart has exceeded the available stock availability for one or more menu item(s)',
            data: {}
          });
        }
      }
    }
   
    let newConvertedDateTime = new Date();
    newConvertedDateTime.setHours(newConvertedDateTime.getHours() + 8);

    order
      .create({
        orderNo: randomNum,
        subTotal: req.body.subTotal,
        cardType: req.body.cardType,
        cardNumber: req.body.cardNumber,
        fundingType: req.body.fundingType,
        transactionFee: req.body.transactionFee,
        promocode_id: req.body.promocode_id,
        promocode_discount: req.body.discount_amount,
        promocode_amount: req.body.promocode_amount ? req.body.promocode_amount : 0,
        pickupCode: pickupCode,
        // tableCode: req.body.table_code ? req.body.table_code.toUpperCase() : '',
        orderServiceType: serviceType,
        total: req.body.total,
        orderDate: new Date(),
        convertedOrderDate: newConvertedDateTime,
        convertedOrderDateTime: newConvertedDateTime,
        userID: userID,
        barID: req.body.barID,
        createdAt: new Date(),
        PreparingStartTime: new Date(),
      })
      .then(async orderResponse => {
        let orderID = orderResponse.id
        let taxIDs = JSON.parse(req.body.tax);   
        let totalTaxAmount = 0;     
        if(taxIDs && taxIDs.length) {
          const taxData = await taxModel.findAll({
            attributes: ['id','name', 'percentage', 'status'],
            where: {id: {in: taxIDs}}
          })

          let orderTaxData = [];
          if(taxData.length) {
            taxData.forEach(tax => {
              orderTaxData.push({
                barID:req.body.barID,
                orderID: orderResponse.id,
                name:tax.dataValues.name,
                percentage: tax.dataValues.percentage,
                taxID: tax.dataValues.id,
                amount: (req.body.subTotal * tax.dataValues.percentage / 100).toFixed(2),
              });
              totalTaxAmount = totalTaxAmount + Number((req.body.subTotal * tax.dataValues.percentage / 100).toFixed(2))
            });
            if(orderTaxData.length) {
              orderTax.bulkCreate(orderTaxData, {returning: true});
            }
          }
        }

        if (productIDs?.length) {
          const uniqueTagIds = await segmentProductTags.findAll({
            where: {
              productID: {
                [Op.in]: productIDs
              }
            },
            attributes: ['tagID'],
            group: ['tagID']
          });

          const tagIds = uniqueTagIds.map(tag => tag.tagID);
          const userBarTagsData = await userBarTags.findAll({
            where: {
              tagID: {
                [Op.in]: tagIds
              },
              barID: req.body.barID,
              userID,
            },
            attributes: ['tagID'],
          });
          const operations=[];
          const existingIds = userBarTagsData.map(elem => elem.tagID);
          const nonExistingIds = uniqueTagIds.filter(elem =>!existingIds.includes(elem.tagID));
          const createData = nonExistingIds.map((elem) => ({ userID, orderID, tagID:elem.tagID, barID: req.body.barID }))
         
          if (existingIds.length) {
            const updateOperation = userBarTags.update(
              {
                orderID,
              },
              {
                where: {
                  tagID: {
                    [Op.in]: existingIds,
                  },
                  barID: req.body.barID,
                  userID,
                },
              }
            );
            operations.push(updateOperation);
          }
          operations.push(userBarTags.bulkCreate(createData))
          await Promise.all(operations);
        }
        
        if(req.body.userConsent != undefined && req.body.userConsent == 1){
          await userConsentVenue.create({
            barID: req.body.barID,
            userID: userID,
            createdAt: moment().tz('Australia/Perth').format('YYYY-MM-DD H:m:s')
          })
        }
        
        //change order Table Number...
        if (tableFlag == true) {
          orderTableNumber.create({
            orderID,
            tableCode: req.body.table_code
          })
          orderResponse.dataValues.tableCode = req.body.table_code
        }
        // configuring POS order details
        // let orderCurrentStateDetails, posConfigured;
        // if (isPOSConfigured) {
        //   const { POS } = require("./POS");
        //   posConfigured = new POS(barData.id)

        //   orderCurrentStateDetails = await posConfigured.handlePosOrder(orderResponse, 'ORDER');
        // }

        // configuring POS order product item details
        for (const requestOrderItem of requestOrderItems) {
          // let productDetail = await commonFunction.getProductDetail(requestOrderItem['productID']);
          const orderItemObj = {
            orderID: orderID,
            productID: requestOrderItem['productID'],
            price: requestOrderItem['price'],
            quantity: requestOrderItem['quantity'],
            specialRequest: requestOrderItem['specialRequest'],
            waitTime: requestOrderItem.productDetail.productWaitTime ? requestOrderItem.productDetail.productWaitTime : '00:10:00',
            createdAt: new Date(),
            PreparingStartTime: new Date(),
          }
          await orderItems
            .create(orderItemObj)
            .then(async orderResponse => {

              if(barData.dataValues.serviceType == 'BOTH' || barData.dataValues.serviceType == serviceType){
                if(barData.dataValues.waitTimeServiceType == 'BOTH' || barData.dataValues.waitTimeServiceType == serviceType){
                  // Create wait time complete notifications
                  await orderItemWaitTimeNotifications.create({
                    orderID: orderID,
                    orderItemID: orderResponse.id,
                    userID: userID,
                    barID: requestOrderItem.productDetail.dataValues.barID,
                    pickupLocationID: requestOrderItem.productDetail.dataValues.pickupLocationID,
                    waitTime: requestOrderItem.productDetail.productWaitTime ? requestOrderItem.productDetail.productWaitTime : '00:10:00',
                    createdAt: new Date(),
                    updatedAt: new Date(),
                  });
                }
              }
                
              let basePrice = Number(orderResponse.price)

              const arrProductItemVariantsItems = []
              const productItemVariantsItems = requestOrderItem['productItemVariants']
              if (Array.isArray(productItemVariantsItems) && productItemVariantsItems.length > 0) {
                for (const productItemVariantsItem of productItemVariantsItems) {
                  const orderVariants = {
                    orderItemID: orderResponse.id,
                    productVariantsID: productItemVariantsItem['productVariantsID'],
                    price: productItemVariantsItem['price'],
                    createdAt: new Date()
                  }
                  arrProductItemVariantsItems.push(orderVariants)
                  basePrice = Number(productItemVariantsItem['price'])
                }
              }

              const arrProductItemExtrasItems = []
              const productItemExtrasItems = requestOrderItem['productItemExtras']
              if (Array.isArray(productItemExtrasItems) && productItemExtrasItems.length > 0) {
                for (const productItemExtrasItem of productItemExtrasItems) {
                  const orderItemExtras = {
                    orderItemID: orderResponse.id,
                    productExtrasID: productItemExtrasItem['productExtrasID'],
                    price: productItemExtrasItem['price'],
                    createdAt: new Date()
                  }
                  arrProductItemExtrasItems.push(orderItemExtras)
                  basePrice += Number(productItemExtrasItem['price'])
                }
              }

              const productVariantTypes = requestOrderItem['productVariantTypes'];
              if (Array.isArray(productVariantTypes) && productVariantTypes.length > 0) {
                for (const productVariantType of productVariantTypes) {
                  orderProductVariantTypes.create({
                    orderItemID: orderResponse.id,
                    productVariantTypeID: productVariantType['productVariantTypeID'],
                    createdAt: new Date(),
                  }).then(async (productVariantTypesData) => {
                    orderProductVariantSubTypes.create({
                      orderProductVariantTypeID: productVariantTypesData.id,
                      productVariantTypeID: productVariantType.productVariantTypeID,
                      productVariantSubTypeID: productVariantType.productVariantSubTypes.productVariantSubTypeID,
                      orderItemID: orderResponse.id,
                      price: productVariantType.productVariantSubTypes.price
                    });
                  });
                  basePrice += Number(productVariantType.productVariantSubTypes.price);
                }
              }
              if(req.body.discount_amount){
                const discountedPrice =  basePrice - (basePrice * (req.body.discount_amount / 100))
                await orderResponse.update({ discountedAmount: discountedPrice, chargeAmount: basePrice })
              }else{
                await orderResponse.update({ discountedAmount: basePrice, chargeAmount: basePrice })
              }
              if (arrProductItemVariantsItems.length > 0) {
                orderItemVariants.bulkCreate(arrProductItemVariantsItems)
                // if (isPOSConfigured)
                //   orderCurrentStateDetails = await posConfigured.handlePosOrder(arrProductItemVariantsItems, 'VARIANTS', orderCurrentStateDetails)
              }

              if (arrProductItemExtrasItems.length > 0) {
                orderItemExtras.bulkCreate(arrProductItemExtrasItems)
              }
            })
          // if (isPOSConfigured)
          //   orderCurrentStateDetails = await posConfigured.handlePosOrder(requestOrderItem, 'ITEMS', orderCurrentStateDetails)
        }
        if(req.body.paymentType != '1'){
          var posOrderFee = 0
          var isPosOrder = '0'
          if(barData.dataValues.posStatus == 1){
            posOrderFee = barData.dataValues.posFee
            isPosOrder = '1'
          }
          var docketOrderFee = 0
          var isDocketOrder = '0'
          if(barData.dataValues.docketStatus == 1){
            docketOrderFee = barData.dataValues.docketCommission
            isDocketOrder = '1'
          }
          await orderResponse.update({
            paymentType: req.body.paymentType,
            paymentStatus: 'notReceived',
            posOrderFee: posOrderFee,
            isPosOrder: isPosOrder,
            docketOrderFee: docketOrderFee,
            isDocketOrder: isDocketOrder
          })
          cartItems.destroy({
            where: {
              userID: userID
            }
          })
          return res.status(200).send({
            success: 1,
            message: 'Order placed successfully!',
            data: orderResponse
          })
        }else{
          try {
            const source = await stripe.sources.create({
              customer: userData.stripeID,
              original_source: req.body.cardid,
            }, {
              stripeAccount: barData.stripeID,
            });
            let myTabFee = req.body.transactionFee
            let totalFee = Number(myTabFee)

            var docketOrderFee = 0
            var isDocketOrder = '0'
            if(barData.dataValues.docketStatus == 1){
              let commission = barData.dataValues.docketCommission > 0 ? barData.dataValues.docketCommission : 0;
              let docketCommission = Number(((commission / 100) * (parseFloat(req.body.subTotal) + parseFloat(totalTaxAmount))).toFixed(2))
              totalFee = Number(totalFee) + Number(docketCommission)
              docketOrderFee = commission
              isDocketOrder = '1'
            }

            
            var posOrderFee = 0
            var isPosOrder = '0'
            if(barData.dataValues.posStatus == 1){
              let commission = parseFloat(barData.dataValues.posFee) > 0 ? barData.dataValues.posFee : 0;
              let posFee = Number(((commission / 100) * (parseFloat(req.body.subTotal) + parseFloat(totalTaxAmount))).toFixed(2))
              totalFee = Number(totalFee) + Number(posFee)
              posOrderFee = commission
              isPosOrder = '1'
            }
            totalFee = Number(totalFee.toFixed(2))
            let payAmount = Math.round(req.body.total * 100)
            await stripe.charges.create(
              {
                amount: payAmount,
                currency: 'aud',
                source: source.id,
                description: `ORDER ID #${orderResponse.orderNo}`,
                transfer_group: orderID,
                application_fee_amount: Math.round((totalFee * 100)),
              },
              {
                stripeAccount: barData.stripeID
              },
              async function (err, charge) {
                if (err == null) {
                  for (const requestOrderItem of requestOrderItems) {
                    if(requestOrderItem.productDetail && requestOrderItem.productDetail.isStockLimit == "Yes"){
                      product.update({
                        stock: Number(requestOrderItem.productDetail.stock) - requestOrderItem['quantity'],
                      },{
                        where: {
                          id: requestOrderItem['productID']
                        },
                      })
                    }
                  }
                  const chargeData = await stripe.charges.retrieve(charge.id, {
                    expand: ['balance_transaction']
                  }, {
                    stripeAccount: barData.stripeID,
                  });
                  var stripeFee = 0 ;
                  if(chargeData.balance_transaction.fee_details){
                    chargeData.balance_transaction.fee_details.map((fee) => {
                      if(fee.type == 'stripe_fee' || fee.type == 'tax'){
                        stripeFee = stripeFee + (fee.amount / 100)
                      }
                    });
                  }
                  stripeFee = parseFloat(stripeFee.toFixed(2))
                 const ordercreate1 =  await orderResponse.update(
                    {
                      paymentStatus: 'received',
                      transactionID: charge.id,
                      transferID: charge.transfer,
                      cardType: charge.payment_method_details.card.brand,
                      cardNumber: charge.payment_method_details.card.last4,
                      posOrderFee: posOrderFee,
                      isPosOrder: isPosOrder,
                      docketOrderFee: docketOrderFee,
                      isDocketOrder: isDocketOrder,
                      stripeFee: stripeFee
                    }
                  )

                  console.log(ordercreate1,"ordercreate1")
                 const trans =  transLogsModel.create({
                    orderID: orderID,
                    amout: req.body.total,
                    transaction_type: 'new_order',
                    transactionID: charge.id,
                    transferID: charge.transfer,
                    log: JSON.stringify(charge),
                    userID: userID,
                    barID: orderResponse.barID,
                    createdAt: new Date()
                  });
                  console.log(trans,"trans");
                  // if (isPOSConfigured) {
                  //   const subTotal = parseFloat(orderResponse.subTotal)
                  //   let transactionObj = {
                  //     reference: charge.transfer,
                  //     invoice: orderResponse.orderNo,
                  //     method: orderResponse.cardType.toLowerCase(),
                  //     stripeAmount: parseFloat(orderResponse.total).toFixed(2),
                  //     subTotal,
                  //     promocodeAmount: orderResponse.promocode_amount ? orderResponse.promocode_amount : 0,
                  //   };
                  //   orderCurrentStateDetails = await posConfigured.handlePosOrder(transactionObj, 'TRANSACTION', orderCurrentStateDetails)
                  //   await posConfigured.submitPosOrder(orderCurrentStateDetails, orderID)
                  // }

                  let message
                  if (tableFlag){
                     console.log(tableFlag,"tableFlag11")
                    message = `You have received a new order from table number # ${req.body.table_code}`
                   } else
                    message = `You have received a new order. Pickup Code is # ${pickupCode}`
                  const bar1 = barNotification.create({
                    barID: orderResponse.barID,
                    notification_type: 'newOrder',
                    userID: userID,
                    dataID: orderID,
                    message: message,
                    createdAt: new Date()
                  })
                  console.log(bar1,"bar oneeee")

                  commonFunction.newOrderNotificationToBarCategoryWise(userID, orderResponse.barID, orderID, 'newOrder', message)
                  console.log("Pointtt 111" )
                  let msg = "Print The Docket"
                  let contentAvailable = 1
                  const bar2 = docketNotification.create({
                    barID: orderResponse.barID,
                    notification_type: 'docketPrint',
                    userID: userID,
                    orderID: orderID,
                    message: msg,
                    contentAvailable: contentAvailable,
                    createdAt: new Date()
                  })
                  console.log(bar2,"docket oneeee create")
                  const printnotification = commonFunction.docketPrintNotification(userID, orderResponse.barID, orderID, 'docketPrint', msg, contentAvailable)
                  console.log(printnotification,"docket oneeee create")

                  cartItems.destroy({ where: { userID: userID } })
                  if(barData.dataValues.posStatus == '1'){
                    if (req.body.order_service_type && req.body.order_service_type.toLowerCase() === 'table') {
                      commonFunction.createOrderinDoshii(barData.dataValues.venueId, orderID, 'table');
                    }else{
                      commonFunction.createOrderinDoshii(barData.dataValues.venueId, orderID, 'pickup');
                    }
                  }
                  return res.status(200).send({
                    success: 1,
                    message: 'Order placed successfully!',
                    data: orderResponse
                  })
                } else {
                  transErrorLogsModel.create({
                    orderID: orderID,
                    amout: req.body.total,
                    transaction_type: 'new_order_err',
                    log: JSON.stringify(err),
                    userID: userID,
                    barID: orderResponse.barID,
                    createdAt: new Date()
                  })

                  order.update(
                    {
                      paymentStatus: 'failed'
                    },
                    {
                      where: {
                        id: orderID
                      }
                    }
                  )
                  if(err.message == 'Your card was declined.'){
                    return res.status(200).send({
                      success: -1,
                      message: `There is an issue with your payment ${err ? ' because ' + err.message : ' Please correct the issue to place your order.'}`
                    })
                  }else{
                    return res.status(200).send({
                      success: -1,
                      message: `Order fail ${err ? ' because ' + err.message : ''}`
                    })
                  }
                }
              }
            )
          } catch (error) {
            console.log("error", error);
            order.update(
              {
                paymentStatus: 'failed'
              },
              {
                where: {
                  id: orderID
                }
              }
            )
            return res.status(200).send({
              success: 0,
              message: 'payment fail!'
            })
          }
        }
      }).error(function (err) {
        return res.status(200).json({
          success: 0,
          message: err.message,
        })
      })
  } catch (error) {
    console.log(error)
    return res.status(200).send({
      success: 0,
      message: error.message
    })
  }
}

exports.updateOrderPaymentStatus = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var userID = sessionData.userID
  try {
    const orderDetails = await order.findOne({ where: { id: req.body.id, paymentStatus: 'notReceived' } })
    if (orderDetails) {
      const barData = await bar.findOne({
        attributes: [
          'id', 'restaurantName', 'managerName', 'email', 'countryCode', 'mobile',
          'stripeID', 'venueId', 'attachedPosConfig','posStatus'
        ],
        where: {
          id: orderDetails.barID
        },
      })
      let orderDescription = '';
      if(orderDetails.paymentType == 2){
        orderDescription = `APAY ORDER ID #${orderDetails.orderNo}`;
      }else if(orderDetails.paymentType == 3){
        orderDescription = `GPAY ORDER ID #${orderDetails.orderNo}`;
      }else{
        orderDescription = `ORDER ID #${orderDetails.orderNo}`;
      }
      if(orderDetails.userID === userID && orderDetails.paymentType !== 1){
        await stripe.paymentIntents.update(req.body.paymentIntentId,
        { description: orderDescription },
        {
          stripeAccount: barData.stripeID,
        });

        const paymentIntentData = await stripe.paymentIntents.retrieve(req.body.paymentIntentId, {
          expand: ['latest_charge.balance_transaction']
        }, {
          stripeAccount: barData.stripeID,
        });
        if (paymentIntentData.latest_charge) {
          if (paymentIntentData.latest_charge.status == 'succeeded') {
            var stripeFee = 0;
            if(paymentIntentData.latest_charge.balance_transaction.fee_details){
              paymentIntentData.latest_charge.balance_transaction.fee_details.map((fee) => {
                if(fee.type == 'stripe_fee' || fee.type == 'tax'){
                  stripeFee = stripeFee + (fee.amount / 100)
                }
              });
              stripeFee = parseFloat(stripeFee.toFixed(2))
            }
            await order.update({
              paymentIntentId: req.body.paymentIntentId,
              transactionID: paymentIntentData.latest_charge.id,
              paymentStatus: 'received',
              transferID: paymentIntentData.latest_charge.transfer ? paymentIntentData.latest_charge.transfer : '',
              cardType: paymentIntentData.latest_charge.payment_method_details.card.brand,
              cardNumber: paymentIntentData.latest_charge.payment_method_details.card.last4,
              stripeFee: stripeFee
            },
            {
              where: {
                id: orderDetails.id
              }
            })
        
            const orderItemsResponse = await orderItems.findAll({where: {orderID: orderDetails.id}});
            for (const requestOrderItem of orderItemsResponse) {
              let productDetail = await commonFunction.getProductDetail(requestOrderItem.productID);
              if(productDetail && productDetail.isStockLimit == 'Yes'){
                product.update({
                  stock: Number(productDetail.stock) - requestOrderItem.quantity,
                },{
                  where: {
                    id: requestOrderItem.productID
                  }
                })
              }
            }
            
            transLogsModel.create({
              orderID: orderDetails.id,
              amout: orderDetails.total,
              transaction_type: 'new_order',
              transactionID: paymentIntentData.latest_charge.id,
              transferID: paymentIntentData.latest_charge.transfer ? paymentIntentData.latest_charge.transfer : '',
              log: JSON.stringify(paymentIntentData.latest_charge),
              userID: userID,
              barID: orderDetails.barID,
              createdAt: new Date()
            });
            let message
            if (orderDetails.orderServiceType != 'PICKUP'){
              let table_code = await orderTableNumber.findOne({
                attributes: ['tableCode'],
                where: {orderID: orderDetails.id},
                order: [['id', 'DESC']]
              })
              const tableCode = table_code && table_code.tableCode
              message = `You have received a new order from table number # ${tableCode}`
            }else{
              console.log("elsee 111" )
              message = `You have received a new order. Pickup Code is # ${orderDetails.pickupCode}`
            }
        
            barNotification.create({
              barID: orderDetails.barID,
              notification_type: 'newOrder',
              userID: userID,
              dataID: orderDetails.id,
              message: message,
              createdAt: new Date()
            })
            commonFunction.newOrderNotificationToBar(userID, orderDetails.barID, orderDetails.id, 'newOrder', message)
            let msg = "Print The Docket"
            let contentAvailable = 1
            const doc1 = docketNotification.create({
              barID: orderDetails.barID,
              notification_type: 'docketPrint',
              userID: userID,
              orderID: orderDetails.id,
              message: msg,
              contentAvailable: contentAvailable,
              createdAt: new Date()
            })
            console.log(doc1,"doc1doc1doc1doc1");

            commonFunction.docketPrintNotification(userID, orderDetails.barID, orderDetails.id, 'docketPrint', msg, contentAvailable)
            if(barData.dataValues.posStatus == '1'){
              commonFunction.createOrderinDoshii(barData.dataValues.venueId, req.body.id, orderDetails.orderServiceType.toLowerCase());
            }
            res.status(200).send({
              success: 1,
              message: 'Order placed successfully!',
              data: orderDetails
            })
          } else {
            order.update({
              paymentStatus: 'failed'
            }, {
              where: {
                id: orderDetails.id
              }
            })
            // Delete wait time notification
            orderItemWaitTimeNotifications.destroy({
              where: {
                orderID: orderDetails.id,
              }
            });
            stripe.paymentIntents.cancel(req.body.paymentIntentId,{
              stripeAccount: barData.stripeID,
            });
            res.status(200).send({
              success: 0,
              message: 'Payment fail!'
            })
          }
        }else{
          order.update({
            paymentStatus: 'failed',
          },{
            where: {
              id: orderDetails.id
            }
          })
          // Delete wait time notification
          orderItemWaitTimeNotifications.destroy({
            where: {
              orderID: orderDetails.id,
            }
          });
          stripe.paymentIntents.cancel(req.body.paymentIntentId,{
            stripeAccount: barData.stripeID,
          });
          res.status(200).send({
            success: 0,
            message: 'Payment fail!'
          })
        }
      }else{
        res.status(200).json({
          success: 0,
          message: 'Order not found!',
        })
      }
    }else{
      const orderDetails = await order.findOne({ where: { id: req.body.id } })
      if(orderDetails){
        if(orderDetails.paymentStatus == 'received'){
          res.status(200).json({
            success: 1,
            message: 'Order placed successfully!',
            data: orderDetails
          })
        }else{
          res.status(200).json({
            success: 0,
            message: 'Payment fail!'
          })
        }
      }else{
        res.status(200).json({
          success: 0,
          message: 'Order not found!',
        })
      }
    }
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: error.message
    })
  }
}

exports.generateClientSecret = async (req, res) => {
  try {
    let { barID, total, subTotal, transactionFee, totalTaxAmount, orderID } = req.body
    const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    let userID = sessionData.userID
 
    const barData = await bar.findOne({
      where: {
        id: barID
      }
    });

    let myTabFee = Number(transactionFee)
    if(barData.dataValues.docketStatus == 1){
      let commission = barData.dataValues.docketCommission > 0 ? barData.dataValues.docketCommission : 0;
      let docketCommission = Number(((commission / 100) * (parseFloat(subTotal) + parseFloat(totalTaxAmount))).toFixed(2))
      myTabFee = Number(myTabFee) + Number(docketCommission)
    }else{
      myTabFee = Number(myTabFee)
    }
    
    if(barData.dataValues.posStatus == 1){
      let commission = parseFloat(barData.dataValues.posFee) > 0 ? barData.dataValues.posFee : 0;
      let posFee = Number(((commission / 100) * (parseFloat(subTotal) + parseFloat(totalTaxAmount))).toFixed(2))
      myTabFee = Number(myTabFee) + Number(posFee)
    }else{
      myTabFee = Number(myTabFee)
    }
    // let stripePerTransactionFee = (total * 0.029 + 0.3).toFixed(2) // This was 2.9% as per international card
    let payAmount = Number(Math.round(total * 100))
    myTabFee = Math.round(myTabFee * 100)

    const paymentIntent = await stripe.paymentIntents.create({
      amount: payAmount,
      currency: 'aud',
      application_fee_amount: myTabFee
    },{
      stripeAccount: barData.stripeID,
    });
    if(paymentIntent) {
      order.update(
        {
          paymentIntentId: paymentIntent.id,
          updatedAt: new Date()
        },
        {
          where: {
            id: orderID
          }
        }
      )
      res.json({
        success: 1,
        data: {paymentIntent:paymentIntent , stripeID:barData.stripeID},
        message: 'Payment intent created.'
      })
    }else {
      res.json({
        success: 0,
        message: 'Payment intent not created.'
      })
    }
  } catch (error) {
    console.log("error", error);
    res.status(200).send({
      success: 0,
      message: error
    })
  }
}

exports.stripeWebhook = async (req, res) => {
  try {
    await sleep(10000);
    const orderDetails = await order.findOne({ where: { paymentIntentId: req.body.data.object.id, paymentStatus: 'notReceived' }})
    if (orderDetails) {
      const barData = await bar.findOne({
        attributes: [
          'id', 'restaurantName', 'managerName', 'email', 'countryCode', 'mobile',
          'stripeID', 'venueId', 'attachedPosConfig','posStatus'
        ],
        where: {
          id: orderDetails.barID
        },
      })
      let orderDescription = '';
      if(orderDetails.paymentType == 2){
        orderDescription = `APAY ORDER ID #${orderDetails.orderNo}`;
      }else if(orderDetails.paymentType == 3){
        orderDescription = `GPAY ORDER ID #${orderDetails.orderNo}`;
      }else{
        orderDescription = `ORDER ID #${orderDetails.orderNo}`;
      }
      if(orderDetails.paymentType !== 1){
        await stripe.paymentIntents.update(req.body.data.object.id,
        { description: orderDescription },
        {
          stripeAccount: barData.stripeID,
        });

        const paymentIntentData = await stripe.paymentIntents.retrieve(req.body.data.object.id, {
          expand: ['latest_charge.balance_transaction']
        }, {
          stripeAccount: barData.stripeID,
        });
        if (paymentIntentData.latest_charge) {
          if (paymentIntentData.latest_charge.status == 'succeeded') {
            var stripeFee = 0;
            if(paymentIntentData.latest_charge.balance_transaction.fee_details){
              paymentIntentData.latest_charge.balance_transaction.fee_details.map((fee) => {
                if(fee.type == 'stripe_fee' || fee.type == 'tax'){
                  stripeFee = stripeFee + (fee.amount / 100)
                }
              });
              stripeFee = parseFloat(stripeFee.toFixed(2))
            }
            await order.update({
              paymentIntentId: req.body.paymentIntentId,
              transactionID: paymentIntentData.latest_charge.id,
              paymentStatus: 'received',
              transferID: paymentIntentData.latest_charge.transfer ? paymentIntentData.latest_charge.transfer : '',
              cardType: paymentIntentData.latest_charge.payment_method_details.card.brand,
              cardNumber: paymentIntentData.latest_charge.payment_method_details.card.last4,
              stripeFee: stripeFee
            },
            {
              where: {
                id: orderDetails.id
              }
            })
        
            const orderItemsResponse = await orderItems.findAll({where: {orderID: orderDetails.id}});
            for (const requestOrderItem of orderItemsResponse) {
              let productDetail = await commonFunction.getProductDetail(requestOrderItem.productID);
              if(productDetail && productDetail.isStockLimit == 'Yes'){
                product.update({
                  stock: Number(productDetail.stock) - requestOrderItem.quantity,
                },{
                  where: {
                    id: requestOrderItem.productID
                  }
                })
              }
            }
            
            transLogsModel.create({
              orderID: orderDetails.id,
              amout: orderDetails.total,
              transaction_type: 'new_order',
              transactionID: paymentIntentData.latest_charge.id,
              transferID: paymentIntentData.latest_charge.transfer ? paymentIntentData.latest_charge.transfer : '',
              log: JSON.stringify(paymentIntentData.latest_charge),
              userID: orderDetails.userID,
              barID: orderDetails.barID,
              createdAt: new Date()
            });

            let message
            if (orderDetails.orderServiceType != 'PICKUP'){
              let table_code = await orderTableNumber.findOne({
                attributes: ['tableCode'],
                where: {orderID: orderDetails.id},
                order: [['id', 'DESC']]
              })
              const tableCode = table_code && table_code.tableCode
              message = `You have received a new order from table number # ${tableCode}`
            }else{
               console.log("elsee 222" )
              message = `You have received a new order. Pickup Code is # ${orderDetails.pickupCode}`
            }
        
           const bar3 = barNotification.create({
              barID: orderDetails.barID,
              notification_type: 'newOrder',
              userID: orderDetails.userID,
              dataID: orderDetails.id,
              message: message,
              createdAt: new Date()
            })
            console.log(bar3,"bar 333333333")
            commonFunction.newOrderNotificationToBar(orderDetails.userID, orderDetails.barID, orderDetails.id, 'newOrder', message)

            if(barData.dataValues.posStatus == '1'){
              if(orderDetails.posOrderId == null || orderDetails.posOrderId == ''){
                commonFunction.createOrderinDoshii(barData.dataValues.venueId, orderDetails.id, orderDetails.orderServiceType.toLowerCase());
              }
            }
            res.status(200).send({
              success: 1,
              message: 'Payment updated successfully!',
            })
          } else {
            order.update({
              paymentStatus: 'failed'
            }, {
              where: {
                id: orderDetails.id
              }
            })
            // Delete wait time notification
            orderItemWaitTimeNotifications.destroy({
              where: {
                orderID: orderDetails.id,
              }
            });
            stripe.paymentIntents.cancel(req.body.data.object.id,{
              stripeAccount: barData.stripeID,
            });
            res.status(200).send({
              success: 0,
              message: 'Payment fail!'
            })
          }
        }else{
          order.update({
            paymentStatus: 'failed',
          },{
            where: {
              id: orderDetails.id
            }
          })
          // Delete wait time notification
          orderItemWaitTimeNotifications.destroy({
            where: {
              orderID: orderDetails.id,
            }
          });
          stripe.paymentIntents.cancel(req.body.data.object.id,{
            stripeAccount: barData.stripeID,
          });
          res.status(200).send({
            success: 0,
            message: 'Payment fail!'
          })
        }
      }else{
        res.status(200).json({
          success: 0,
          message: 'Order not found!',
        })
      }
    }else{
      res.status(200).json({
        success: 0,
        message: 'Order not found!',
      })
    }
  } catch (error) {
    console.log("error", error);
    res.status(200).send({
      success: 0,
      message: error
    })
  }
}

exports.orderCancel = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  try {
    order
      .findOne({
        where: {
          id: req.body.id,
          barID: barID
        },
        include:[
          {
            attributes: [
              'id', 'orderID', 'name', 'percentage', "taxID", "amount"
            ],
            model: orderTax,
          },
          {
            attributes: [
              'id', 'orderID', 'name', 'percentage', "taxID", "amount"
            ],
            model: orderRefundTax,
          },
        ]
      })
      .then(async orderDataResponse => {
        if (orderDataResponse && Object.keys(orderDataResponse).length > 0) {
          var orderItemWhereClause = {}
          orderItemWhereClause['orderID'] = req.body.id

          var orderUpdateData = {}
          orderUpdateData['refundStatus'] = 'PartialRefunded'
          orderUpdateData['updatedAt'] = new Date()
          orderUpdateData['refundedDate'] = new Date()

          var orderItemsWithQty = JSON.parse(req.body.orderItemsID);
          let itemIds = []
          for (const item of orderItemsWithQty) {
            itemIds.push(item.id)
          }
          orderItemWhereClause['id'] = { [Op.in]: itemIds }

          orderItems
            .findAll({
              attributes: [
                'id',
                'orderID',
                'productID',
                'price',
                'chargeAmount',
                'discountedAmount',
                'quantity',
                'refundedQuantity',
                'refundAmount',
                'newRefundAmount'
              ],
              where: orderItemWhereClause
            })
            .then(async orderItemRes => {
              let totalRefundAmount = 0;
              let totalDiscountedAmount = 0;

              await Promise.all(orderItemRes.map(async orderItem => {
                let refundedQuantity = 0
                let refundedAmount = 0
                let newRefundAmount = 0
                let isCanceled = 'Yes'

                let rQty = 0
                for (const item of orderItemsWithQty) {
                  if (item.id === orderItem.id) {
                    rQty = item.quantity
                  }
                }
                let itemRefundedAmount = orderItem.chargeAmount * rQty
                let itemRefundedDiscountedAmount = orderItem.discountedAmount * rQty
                refundedAmount = orderItem.refundAmount + (orderItem.chargeAmount * rQty)
                newRefundAmount = orderItem.newRefundAmount + (orderItem.discountedAmount * rQty)
                refundedQuantity = orderItem.refundedQuantity + rQty
                totalRefundAmount = totalRefundAmount + itemRefundedAmount
                isCanceled = refundedQuantity === orderItem.quantity ? 'Yes' : 'No';
                
                totalDiscountedAmount = totalDiscountedAmount + itemRefundedDiscountedAmount

                await orderItems.update(
                  {
                    isCanceled: isCanceled,
                    refundAmount: refundedAmount,
                    newRefundAmount: newRefundAmount,
                    refundedQuantity: refundedQuantity,
                    updatedAt: new Date()
                  },
                  {
                    returning: true,
                    where: {
                      id: orderItem.id
                    }
                  }
                )
                
                // Delete wait time notification
                orderItemWaitTimeNotifications.destroy(
                  {
                    where: {
                      orderItemID: orderItem.id
                    }
                  }
                )
              }))

              const orderItemCount = await orderItems.findAll({
                attributes: [
                  'id'
                ],
                where: {
                  orderID: req.body.id,
                  isCanceled: 'No'
                }
              })

              if (orderItemCount.length == 0) {
                orderUpdateData['isCanceled'] = 'Yes'
                orderUpdateData['refundStatus'] = 'Refunded'
                orderUpdateData['orderStatus'] = 'Pickedup'
              }

              let taxAmount = 0;
              
              let reqTaxData = JSON.parse(req.body.tax_data ? req.body.tax_data : '{}');

              let orderTaxIds = orderDataResponse.order_taxes && orderDataResponse.order_taxes.map(a => a.taxID);
              let orderRefundTaxIds = orderDataResponse.order_refund_taxes && orderDataResponse.order_refund_taxes.map(a => a.taxID);

              const orederRemainingTax = orderTaxIds.filter(element => !orderRefundTaxIds.includes(element));
              
              let oldTaxAmountData = orderDataResponse.order_taxes && orderDataResponse.order_taxes.map(item => item.amount);

              let orderOldTaxAmount = oldTaxAmountData.length ? oldTaxAmountData.reduce((prev, next) => prev + next) : 0;

              let refundOldTaxAmountData = orderDataResponse.order_refund_taxes && orderDataResponse.order_refund_taxes.map(item => item.amount);
              
              let totalOldRefundTaxAmount = refundOldTaxAmountData.length ? refundOldTaxAmountData.reduce((prev, next) => prev + next) : 0;

              if(reqTaxData && reqTaxData.length) {
                let taxIDs = reqTaxData.map(a => a.tax);
                const taxData = await taxModel.findAll({
                  attributes: ['id','name', 'percentage', 'status'],
                  where: {id: {in: taxIDs}}
                });
                let orderTaxData = [];
                let refundTaxAmount = 0;
                taxData.forEach(async tax => {
                  let calculateTax = (totalRefundAmount * tax.dataValues.percentage) / 100; 
                  taxAmount += calculateTax;     
                  
                  let reqTaxObj = reqTaxData.find(o => o.tax === tax.dataValues.id);
                  
                  let getTaxRefundData = orderDataResponse.order_refund_taxes && orderDataResponse.order_refund_taxes.find(o => req.body.id == o.dataValues.orderID && tax.dataValues.id == o.dataValues.taxID);
                  if(getTaxRefundData) {
                    refundTaxAmount += reqTaxObj.amount + getTaxRefundData.dataValues.amount;
                    await orderRefundTax.update({
                      amount: reqTaxObj.amount + getTaxRefundData.dataValues.amount      
                    },
                    {
                      where: {
                        id: getTaxRefundData.dataValues.id,
                      }
                    });
                  }else{
                    refundTaxAmount += reqTaxObj.amount;
                    orderTaxData.push({
                      barID:barID,
                      orderID: req.body.id,
                      name:tax.dataValues.name,
                      percentage: tax.dataValues.percentage,
                      taxID: tax.dataValues.id,
                      amount:reqTaxObj.amount,
                    });
                  }
                });

                refundTaxAmount = Number(refundTaxAmount.toFixed(2))

                if(orderTaxData.length) {
                  orderRefundTax.bulkCreate(orderTaxData, {returning: true});
                }
                
                let notPresentInData = orederRemainingTax.filter(val => !taxIDs.includes(val));  
                
                // let orderTaxAmount = orderDataResponse.order_taxes && orderDataResponse.order_taxes.map(item => item.amount).reduce((prev, next) => prev + next);

                // let refundTaxAmountData = orderDataResponse.order_refund_taxes && orderDataResponse.order_refund_taxes.map(item => item.amount);
                // let totalRefundTaxAmount = refundTaxAmountData.length ? refundTaxAmountData.reduce((prev, next) => prev + next) : 0;
                
                // console.log("notPresentInData", notPresentInData);
                // console.log("refundTaxAmount < orderTaxAmount", totalRefundTaxAmount, refundTaxAmount, orderTaxAmount);
                
                if(notPresentInData.length || (totalOldRefundTaxAmount + refundTaxAmount) < orderOldTaxAmount) {
                  orderUpdateData['refundStatus'] = 'PartialRefunded'
                  orderUpdateData['isCanceled'] = 'No'
                }

                orderUpdateData['tax'] = taxAmount;
              }else if(reqTaxData.length == 0 && totalOldRefundTaxAmount < orderOldTaxAmount) {
                orderUpdateData['refundStatus'] = 'PartialRefunded'
                orderUpdateData['isCanceled'] = 'No'
              }
              let refundAmount = 0;     
              if(orderDataResponse.refundTransactionFee == 'No' && req.body.refundTransactionFee == 'Yes'){
                refundAmount =  Math.round((totalDiscountedAmount + taxAmount + orderDataResponse.transactionFee) * 100)
                orderUpdateData['refundTransactionFee'] = 'Yes'
              }else{
                refundAmount = Math.round((totalDiscountedAmount + taxAmount) * 100)
              }
              order
                .update(orderUpdateData,
                  {
                    returning: true,
                    where: {
                      id: req.body.id,
                      barID: barID
                    }
                  }
                )
                .then(async function () {
                  if (orderDataResponse.transactionID != '' && totalDiscountedAmount > 0) {
                    const barData = await bar.findOne({
                      attributes: [
                        'id', 'restaurantName', 'managerName', 'email', 'countryCode', 'mobile',
                        'stripeID', 'venueId', 'attachedPosConfig', 'posStatus'
                      ],
                      where: {
                        id: barID
                      },
                    })
                    await stripe.refunds.create({
                      charge: orderDataResponse.transactionID,
                      amount: refundAmount,
                      // reverse_transfer: true,
                      refund_application_fee: false
                    },
                    {
                      stripeAccount: barData.stripeID
                    }, async function (stripeErr, refundsData) {
                      if (stripeErr == null) {
                        if (refundsData.status == 'succeeded') {
                          // to handle refund in POS
                          // if (orderDataResponse.posOrderId) {
                          //   const { POS } = require("./POS");
                          //   const posConfigured = new POS(barID)
                          //   await posConfigured.handlePosOrderRefund(orderDataResponse.posOrderId, totalRefundAmount, orderItemsWithQty, refundsData)
                          // }

                          transLogsModel.create({
                            orderID: orderDataResponse.id,
                            amout: refundAmount,
                            transaction_type: 'cancel_order',
                            refundTransactionID: refundsData.id,
                            reversalsTransactionID: refundsData.transfer_reversal,
                            log: JSON.stringify(refundsData),
                            userID: orderDataResponse.userID,
                            barID: barID,
                            createdAt: new Date()
                          })
                        } else {
                          transErrorLogsModel.create({
                            orderID: orderDataResponse.id,
                            amout: refundAmount,
                            transaction_type: 'cancel_order_err',
                            log: JSON.stringify(refundsData),
                            userID: orderDataResponse.userID,
                            barID: barID,
                            createdAt: new Date()
                          })
                        }
                      } else {
                        transErrorLogsModel.create({
                          orderID: orderDataResponse.id,
                          amout: refundAmount,
                          transaction_type: 'cancel_order_err',
                          log: JSON.stringify(stripeErr),
                          userID: orderDataResponse.userID,
                          barID: barID,
                          createdAt: new Date()
                        })
                      }
                    })

                    // var message = 'You Order ID is #' + orderDataResponse.orderNo + ' Refunded.'

                    // Table code seperated for new change
                    let table_code = await orderTableNumber.findOne({
                      attributes: ['tableCode'],
                      where: {orderID: orderDataResponse.id},
                      order: [['id', 'DESC']]
                    })
                    const tableCode = table_code && table_code.tableCode

                    var message = orderDataResponse.orderServiceType === 'PICKUP' ? 
                                    `Your order ${orderDataResponse.pickupCode} has been refunded.` 
                                  : `Your order for table #${tableCode} has been refunded.`;

                    var notification_type = 'orderRefund'

                    if (message != '') {
                      userNotification.create({
                        barID: barID,
                        notification_type: notification_type,
                        userID: orderDataResponse.userID,
                        dataID: req.body.id,
                        message: message,
                        createdAt: new Date()
                      })
                      commonFunction.orderStatusNotificationToUser(orderDataResponse.userID, req.body.id, notification_type, message, "Order Refund")
                    }
                  }
                  res.json({
                    success: 1,
                    message: 'success!'
                  })
                }).error(function (err) {
                  res.status(200).json({
                    success: 0,
                    message: err.message,
                  })
                })
            })
        } else {
          res.status(200).json({
            success: 0,
            message: 'Order not found!',
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!',
      data: error
    })
  }
}

exports.intoxicated = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  try {
    order
      .findOne({
        where: {
          id: req.body.id,
          barID: barID
        }
      })
      .then(async orderDataResponse => {
        if (orderDataResponse) {
          orderDataResponse.update({
            orderStatus: 'Intoxicated',
            intoxicatedDate: new Date(),
            updatedAt: new Date(),
          });
          
          // Delete wait time notification
          orderItemWaitTimeNotifications.destroy(
            {
              where: {
                orderID: req.body.id
              }
            }
          )

          var message = 'Your account has been marked as too intoxicated by the venue. Please contact the venue.'
          var notification_type = 'orderIntoxicated'

          if (message != '') {
            userNotification.create({
              barID: barID,
              notification_type: notification_type,
              userID: orderDataResponse.userID,
              dataID: req.body.id,
              message: message,
              createdAt: new Date()
            })
            commonFunction.orderStatusNotificationToUser(orderDataResponse.userID, req.body.id, notification_type, message, "Order Intoxicated")
          }
        } else {
          return res.status(200).json({
            success: 0,
            message: 'Order not found!',
          })
        }
        
        // if (orderDataResponse && orderDataResponse.posOrderId) {
        //   const { POS } = require("./POS");
        //   const posConfigured = new POS(barID)
        //   await posConfigured.intoxicatePosOrder(orderDataResponse.posOrderId)
        // }

        res.json({
          success: 1,
          message: 'success!'
        })
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.orderDetail = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var userID = sessionData.userID
  try {
    var whereClauseOrder = []
    whereClauseOrder.push({
      isDeleted: 'No'
    })
    whereClauseOrder.push({
      userID: userID
    })
    whereClauseOrder.push({
      id: req.body.id
    })

    order
      .findAll({
        where: whereClauseOrder,
        attributes: [
          'id',
          'orderNo',
          'pickupCode',
          [Sequelize.literal(`(select tableCode from orderTableNumber where orderID = orders.id order by id desc limit 1)`), 'tableCode'],
          'subTotal',
          'transactionFee',
          'refundTransactionFee',
          'tax',
          'total',
          'orderDate',
          'orderStatus',
          'orderServiceType',
          'paymentType',
          'refundStatus',
          'promocode_id',
          'promocode_amount',
          'promocode_discount',
          'cardType',
          'cardNumber',
          'fundingType',
          'userID',
          'barID',
          'createdAt',
          'refundedDate'
        ],
        include: [
          {
            attributes: [
              'id',
              'orderID',
              'productID',
              'price',
              'quantity',
              'specialRequest',
              'isCanceled',
              'refundAmount',
              'refundedQuantity',
              'waitTime',
              'orderStatus',
              'PreparingStartTime',
              'ReadyTime',
              'PickedupTime',
              [Sequelize.literal(`(ADDTIME(order_items.createdAt, waitTime)) `), 'expectedTime'],
              [Sequelize.literal(`(select address from product INNER JOIN pickup_location ON product.pickupLocationID=pickup_location.id where product.id = order_items.productID ) `), 'pickupLocation'],
              [Sequelize.literal(`(select subCategoryID from product where product.id = order_items.productID ) `), 'subCategoryID'],
            ],
            model: orderItems,
            include: [
              {
                attributes: [
                  'id',
                  'name',
                  'description',
                  'avatar'
                ],
                model: product,
                include: [
                  {
                    attributes: [
                      'id',
                      'description',
                      'address'
                    ],
                    model: pickupLocation,
                  }
                ]
              },
              {
                attributes: [
                  'id',
                  'orderItemID',
                  'productExtrasID',
                  'price'
                ],
                model: orderItemExtras,
                include: [
                  {
                    attributes: [
                      'id',
                      'extraItem'
                    ],
                    model: productExtras,
                  }
                ]
              },
              {
                attributes: [
                  'id',
                  'orderItemID',
                  'productVariantsID',
                  'price'
                ],
                model: orderItemVariants,
                include: [
                  {
                    attributes: [
                      'id',
                      'variantType'
                    ],
                    model: productVariants,
                  }
                ]
              },
              {
                attributes: [
                  ['id', "orderProductVariantTypeID"],
                  'orderItemID',
                ],
                where: Sequelize.where(Sequelize.col('`order_items`.`id`'), Sequelize.col('`order_items->order_product_variant_types`.`orderItemID`')),
                model: orderProductVariantTypes,
                required: false,
                include: [
                  {
                    attributes: [
                      ['id', "productVariantTypeID"],
                      'label',
                    ],
                    model: productVariantTypes,
                    required: true,
                    include: [
                      {
                        attributes: [
                          ['id', "orderProductVariantSubTypeID"],
                          'orderItemID',
                        ],
                        where: Sequelize.where(Sequelize.col('`order_items->order_product_variant_types->product_variant_type->order_product_variant_sub_type`.`orderProductVariantTypeID`'), Sequelize.col('`order_items->order_product_variant_types`.`id`')),
                        model: orderProductVariantSubTypes,
                        include: [
                          {
                            attributes: [
                              ['id', "productVariantSubTypeID"],
                              ['variantType', "extraItem"],
                              'price',
                            ],
                            model: productVariantSubTypes,
                          }
                        ]
                      }
                    ]
                  }
                ],
              }
            ]
          },
          {
            required: true,
            model: bar,
            attributes: ['id', 'restaurantName', 'email', 'businessRegisterId', 'address' ,'avatar']
          },
          {
            model: coupons,
            attributes: ['id', 'code', 'name', 'description']
          },
          {
            attributes: [
              'id','name', 'percentage', "taxID", "amount"
            ],
            model: orderTax,
          },
          {
            attributes: [
              'id','name', 'percentage', "taxID", "amount"
            ],
            model: orderRefundTax,
          },
        ]
      }).then(order => {
        if (order.length) {
          let newOrderItems = groupByOrderItems(order[0].order_items, order[0].orderServiceType, order[0].refundStatus); // Group By order items
          // delete order[0].dataValues.order_items; // Delete old key
          order[0].dataValues['order_items_group'] = newOrderItems; // Add new key to object
          res.status(200).send({
            success: 1,
            message: 'order detail retrive successfully!',
            data: order[0]
          })
        } else {
          res.status(200).json({
            success: 0,
            message: 'No Results Found',
            data: {}
          })
        }
      }).error(function (err) {
        res.status(200).json({
          success: 0,
          message: err.message,
        })
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.newRunningOrder = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var userID = sessionData.userID
  try {
    let whereClauseOrder = []
    let whereClauseOrderItem = []
    let havingClauseOrder = []
    whereClauseOrder.push({
      isDeleted: 'No',
      paymentStatus: 'received',
    })
    whereClauseOrder.push({
      userID: userID,
      orderStatus : {
        [Op.notIn]: ['Intoxicated', 'Pickedup']
      }
    })
    whereClauseOrderItem.push({
      isDeleted: 'No',
      isCanceled: 'No',
    })
    whereClauseOrderItem.push({
      [Op.or]: [
        {
          orderStatus: 'New'
        },
        {
          orderStatus: 'Preparing'
        },
        {
          orderStatus: 'Pickup'
        }
      ]
    })

    whereClauseOrder.push({
      [Op.or]: [
        {
          refundStatus: 'No'
        },
        {
          refundStatus: 'PartialRefunded'
        },
      ]
    })

    havingClauseOrder.push({
      totalCancelItem: {
        [Op.gt]: 0
      }
    });

    order
      .findAll({
        where: whereClauseOrder,
        having: havingClauseOrder,
        attributes: [
          'id',
          'orderNo',
          'pickupCode',
          [Sequelize.literal(`(select tableCode from orderTableNumber where orderID = orders.id order by id desc limit 1)`), 'tableCode'],
          'subTotal',
          'transactionFee',
          'tax',
          'total',
          'orderDate',
          'orderStatus',
          'paymentStatus',
          'refundStatus',
          'orderServiceType',
          'promocode_id',
          'promocode_amount',
          'userID',
          'barID',
          'createdAt',
          [Sequelize.literal(`(select waitTimeServiceType from bar where id = orders.barID )`), 'waitTimeServiceType'],
          [
            Sequelize.literal(
              '(select count(order_items.id) from order_items WHERE order_items.orderID = `orders`.id AND order_items.isCanceled="No")'
            ),
            'totalCancelItem'
          ],
          [
            Sequelize.literal(
              '(select count(order_items.id) from order_items WHERE order_items.orderID = `orders`.id)'
            ),
            'totalOrderItem'
          ],
        ],
        include: [
          {
            attributes: [
              'id',
              'orderID',
              'productID',
              'price',
              'quantity',
              'specialRequest',
              'isCanceled',
              'refundAmount',
              'refundedQuantity',
              'orderStatus',
              'PreparingStartTime',
              'ReadyTime',
              'PickedupTime',
              'waitTime',
              [Sequelize.literal(`(ADDTIME(order_items.createdAt, waitTime)) `), 'expectedTime'],
              [Sequelize.literal(`(select address from product INNER JOIN pickup_location ON product.pickupLocationID=pickup_location.id where product.id = order_items.productID ) `), 'pickupLocation'],
              [Sequelize.literal(`(select subCategoryID from product where product.id = order_items.productID ) `), 'subCategoryID'],
            ],
            model: orderItems,
            where: whereClauseOrderItem,
            include: [
              {
                attributes: [
                  'id',
                  'name',
                  'description',
                  'avatar'
                ],
                model: product,
                include: [
                  {
                    attributes: [
                      'id',
                      'description',
                      'address'
                    ],
                    model: pickupLocation,
                  }
                ]
              },
              {
                attributes: [
                  'id',
                  'orderItemID',
                  'productExtrasID',
                  'price'
                ],
                model: orderItemExtras,
                include: [
                  {
                    attributes: [
                      'id',
                      'extraItem'
                    ],
                    model: productExtras,
                  }
                ]
              },
              {
                attributes: [
                  'id',
                  'orderItemID',
                  'productVariantsID',
                  'price'
                ],
                model: orderItemVariants,
                include: [
                  {
                    attributes: [
                      'id',
                      'variantType'
                    ],
                    model: productVariants,
                  }
                ]
              },
              {
                attributes: [
                  ['id', "orderProductVariantTypeID"],
                  'orderItemID',
                ],
                where: Sequelize.where(Sequelize.col('`order_items`.`id`'), Sequelize.col('`order_items->order_product_variant_types`.`orderItemID`')),
                model: orderProductVariantTypes,
                required: false,
                include: [
                  {
                    attributes: [
                      ['id', "productVariantTypeID"],
                      'label',
                    ],
                    model: productVariantTypes,
                    required: true,
                    include: [
                      {
                        attributes: [
                          ['id', "orderProductVariantSubTypeID"],
                          'orderItemID',
                        ],
                        where: Sequelize.where(Sequelize.col('`order_items->order_product_variant_types->product_variant_type->order_product_variant_sub_type`.`orderProductVariantTypeID`'), Sequelize.col('`order_items->order_product_variant_types`.`id`')),
                        model: orderProductVariantSubTypes,
                        include: [
                          {
                            attributes: [
                              ['id', "productVariantSubTypeID"],
                              ['variantType', "extraItem"],
                              'price',
                            ],
                            model: productVariantSubTypes,
                          }
                        ]
                      }
                    ]
                  }
                ],
              }
            ]
          },
          {
            required: true,
            model: bar,
            attributes: ['id', 'restaurantName']
          },
          {
            model: coupons,
            attributes: ['id', 'code', 'name', 'description']
          }
        ],
        order: [['createdAt', 'DESC'], ['orderStatus', 'DESC']],
        distinct: true,
        duplicating: false
      }).then(orders => {
        if (orders.length > 0) {
          let newOrdersObj = orders.map((order)=>{
            let newOrderItems = groupByOrderItems(order.order_items, order.orderServiceType, order.refundStatus); // Group By order items
            // delete order.dataValues.order_items; // Delete old key
            order.dataValues['order_items_group'] = newOrderItems; // Add new key to object
            return order
          })
          res.status(200).send({
            success: 1,
            message: 'order detail retrive successfully!',
            data: newOrdersObj
          })
        } else {
          res.status(200).json({
            success: 0,
            message: 'No Results Found',
            data: []
          })
        }
      }).error(function (err) {
        res.status(200).json({
          success: 0,
          message: err.message,
        })
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.singleCurrentOrder = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var userID = sessionData.userID
  try {
    var whereClauseOrder = []
    var whereClauseOrderItem = []
    whereClauseOrder.push({
      isDeleted: 'No',
      paymentStatus: 'received'
    })
    whereClauseOrder.push({
      userID: userID,
      orderStatus : {
        [Op.notIn]: ['Intoxicated', 'Pickedup']
      }
    })
    whereClauseOrderItem.push({
      isDeleted: 'No',
      isCanceled: 'No',
    })
    whereClauseOrderItem.push({
      [Op.or]: [
        {
          orderStatus: 'New'
        },
        {
          orderStatus: 'Preparing'
        },
        {
          orderStatus: 'Pickup'
        }
      ]
    })
    whereClauseOrder.push({
      [Op.or]: [
        {
          refundStatus: 'No'
        },
        {
          refundStatus: 'PartialRefunded'
        },
      ]
    })
    
    order
      .findOne({
        where: whereClauseOrder,
        attributes: [
          'id',
          'orderNo',
          'pickupCode',
          [Sequelize.literal(`(select tableCode from orderTableNumber where orderID = orders.id order by id desc limit 1)`), 'tableCode'],
          'subTotal',
          'transactionFee',
          'refundTransactionFee',
          'tax',
          'total',
          'orderDate',
          'orderStatus',
          'refundStatus',
          'orderServiceType',
          'promocode_id',
          'promocode_amount',
          'userID',
          'barID',
          'createdAt',
        ],
        order: [['createdAt', 'DESC'], ['orderStatus', 'DESC']],
        distinct: true,
        duplicating: false,
        include:[
          {
            model: orderItems, 
            where: whereClauseOrderItem,
            attributes: [
              'id',
              'orderID',
              'productID',
              'price',
              'quantity',
              'specialRequest',
              'isCanceled',
              'refundAmount',
              'refundedQuantity',
              'orderStatus',
              'PreparingStartTime',
              'ReadyTime',
              'PickedupTime',
              'waitTime',
              [Sequelize.literal(`(ADDTIME(order_items.createdAt, waitTime)) `), 'expectedTime'],
              [Sequelize.literal(`(select address from product INNER JOIN pickup_location ON product.pickupLocationID=pickup_location.id where product.id = order_items.productID ) `), 'pickupLocation'],
              [Sequelize.literal(`(select subCategoryID from product where product.id = order_items.productID ) `), 'subCategoryID'],
            ],
          }
        ]
      }).then(order => {
        if (order) {
          // let newOrderItems = groupByOrderItems(order.order_items, order.orderServiceType, order.refundStatus); // Group By order items
          // order.dataValues['order_items_group'] = newOrderItems; // Add new key to object
          delete order.dataValues.order_items; // Delete old key
          res.status(200).send({
            success: 1,
            message: 'order detail retrive successfully!',
            data: order
          })
        } else {
          res.status(200).json({
            success: 0,
            message: 'No Results Found',
            data: {}
          })
        }
      }).error(function (err) {
        res.status(200).json({
          success: 0,
          message: err.message,
        })
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.orderHistory = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var userID = sessionData.userID
  try {
    var whereClauseOrder = []
    whereClauseOrder.push({
      isDeleted: 'No'
    })
    whereClauseOrder.push({
      userID: userID,
      paymentStatus:  'received'
    })

    if(req.body.startDate) {
      whereClauseOrder.push({
        convertedOrderDate : {
          [Op.gte]: req.body.startDate
        }
      });
    }
    
    if(req.body.endDate) {
      whereClauseOrder.push({
        convertedOrderDate : {
          [Op.lte]: req.body.endDate
        }
      });
    }

    if(req.body.search) {
      whereClauseOrder.push({
        orderNo : {
          [Op.like]: '%' + req.body.search + '%'
        }
      });
    }

    let page = req.body.page ? req.body.page : 1;
    const offset = (page - 1) * 10;
    const limit = 10;


    const orderHistoryData = await order
      .findAndCountAll({
        where: whereClauseOrder,
        attributes: [
          'id',
          'orderNo',
          'pickupCode',
          [Sequelize.literal(`(select tableCode from orderTableNumber where orderID = orders.id order by id desc limit 1)`), 'tableCode'],
          [Sequelize.literal(`IF(orderStatus='Intoxicated', orderStatus, IF(refundStatus='No', orderStatus, refundStatus) )`), 'orderStatus'],
          'subTotal',
          'transactionFee',
          'refundTransactionFee',
          'tax',
          'total',
          'orderDate',
          'promocode_id',
          'promocode_amount',
          'refundStatus',
          'orderServiceType',
          'cardType',
          'cardNumber',
          'fundingType',
          'userID',
          'barID',
          'createdAt'
        ],
        include: [
          {
            required: true,
            model: bar,
            attributes: ['id', 'restaurantName']
          },
          {
            model: orderItems, 
            attributes: [
              'id',
              'orderID',
              'productID',
              'price',
              'quantity',
              'specialRequest',
              'isCanceled',
              'refundAmount',
              'refundedQuantity',
              'orderStatus',
              'PreparingStartTime',
              'ReadyTime',
              'PickedupTime',
              'waitTime',
              [Sequelize.literal(`(ADDTIME(order_items.createdAt, waitTime)) `), 'expectedTime'],
              [Sequelize.literal(`(select address from product INNER JOIN pickup_location ON product.pickupLocationID=pickup_location.id where product.id = order_items.productID ) `), 'pickupLocation'],
              [Sequelize.literal(`(select subCategoryID from product where product.id = order_items.productID ) `), 'subCategoryID'],
            ],
          },
          {
            attributes: [
              'id','name', 'percentage', "taxID", "amount"
            ],
            model: orderTax,
          },
          {
            attributes: [
              'id','name', 'percentage', "taxID", "amount"
            ],
            model: orderRefundTax,
          },
        ],
        order: [['createdAt', 'DESC']],
        offset: offset,
        limit: limit,
      });
    
    if (orderHistoryData.count > 0) {
      orderHistoryData.rows = orderHistoryData && orderHistoryData.rows.map((order) => {
        let newOrderItems = groupByOrderItems(order.order_items, order.orderServiceType, order.refundStatus); // Group By order items
        delete order.dataValues.order_items; // Delete old key
        order.dataValues['order_items_group'] = newOrderItems; // Add new key to object
        return order
      })
      return res.status(200).send({
        success: 1,
        message: 'order list retrieve successfully!',
        data: orderHistoryData
      })
    } else {
      return res.status(200).json({
        success: 0,
        message: 'No Results Found',
        data: []
      })
    }
  } catch (error) {
    console.log(error);
    return res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.barList = async (req, res) => {
  try {
    const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    const barID = sessionData.barID;

    const dashboardOrder = {}
    const filterType = req.body.filter_type
    let pickupClause = false
    let pickupCategory = [-1]
    let tableClause = false
    let tableCategory = [-1]
    let whereClauseCategoryOrder = []
    let pickupOrder = []
    let newOrder = []
    let havingClauseOrder = [
      {
        totalCancelItem: {
          [Op.gt]: 0
        }
      }
    ];
    
    if (filterType !== '') {
      filterType.split(',').map(
        filterReceived => {
          switch (filterReceived) {
            case "1":
              pickupClause = true
              pickupCategory.push(1)
              break;
            case "2":
              pickupClause = true
              pickupCategory.push(2)
              break;
            case "3":
              tableClause = true
              tableCategory.push(1)
              break;
            case '4':
              tableClause = true
              tableCategory.push(2)
              break;
            default:
              break
          }
        })
      const orderAttributes = [
        'id',
        'orderNo',
        // [Sequelize.literal(`IF(orderServiceType='TABLE', tableCode, pickupCode)`), 'pickupCode'],
        'subTotal',
        'transactionFee',
        'tax',
        'total',
        'orderDate',
        'orderStatus',
        'promocode_id',
        'promocode_amount',
        'promocode_discount',
        'userID',
        'barID',
        'orderServiceType',
        'createdAt',
        'docketPrintingStatus',
        [
          Sequelize.literal(
            '(select count(order_items.id) from order_items WHERE order_items.orderID = `orders`.id AND order_items.isCanceled="No")'
          ),
          'totalCancelItem'
        ],
      ]
      const itemAttributes = [
        'id',
        'orderID',
        'productID',
        'price',
        'quantity',
        'specialRequest',
        'isCanceled',
        'refundAmount',
        'refundedQuantity'
      ]
      const productAttributes = [
        'id',
        'name',
        'categoryID',
        'description',
        'avatar',
        'subCategoryID'
      ]
      let tableServiceOrder
      let pickupServiceOrder;

      if (tableClause) {
        tableServiceOrder = await order
          .findAll({
            where: [{
              orderServiceType: 'TABLE',
            }, {
              isDeleted: 'No',
              paymentStatus: 'received',
              barID: barID,
              isCanceled: 'No',
              [Op.or]: [
                {
                  orderStatus: 'New'
                },
                {
                  orderStatus: 'Preparing'
                }
              ],
            }],
            attributes: orderAttributes,
            include: [
              {
                required: true,
                attributes: itemAttributes,
                model: orderItems,
                include: [
                  {
                    where: { categoryID: tableCategory },
                    attributes: productAttributes,
                    model: product,
                    include: [
                      {
                        attributes: [
                          'id',
                          'description',
                          'address'
                        ],
                        model: pickupLocation,
                      }
                    ]
                  },
                  {
                    attributes: [
                      'id',
                      'orderItemID',
                      'productExtrasID',
                      'price'
                    ],
                    model: orderItemExtras,
                    include: [
                      {
                        attributes: [
                          'id',
                          'extraItem'
                        ],
                        model: productExtras,
                      }
                    ]
                  },
                  {
                    attributes: [
                      'id',
                      'orderItemID',
                      'productVariantsID',
                      'price'
                    ],
                    model: orderItemVariants,
                    include: [
                      {
                        attributes: [
                          'id',
                          'variantType'
                        ],
                        model: productVariants,
                      }
                    ]
                  },
                  {
                    attributes: [
                      ['id', "orderProductVariantTypeID"],
                      'orderItemID',
                    ],
                    where: Sequelize.where(Sequelize.col('`order_items`.`id`'), Sequelize.col('`order_items->order_product_variant_types`.`orderItemID`')),
                    model: orderProductVariantTypes,
                    required: false,
                    include: [
                      {
                        attributes: [
                          ['id', "productVariantTypeID"],
                          'label',
                        ],
                        model: productVariantTypes,
                        required: true,
                        include: [
                          {
                            attributes: [
                              ['id', "orderProductVariantSubTypeID"],
                              'orderItemID',
                            ],
                            where: Sequelize.where(Sequelize.col('`order_items->order_product_variant_types->product_variant_type->order_product_variant_sub_type`.`orderProductVariantTypeID`'), Sequelize.col('`order_items->order_product_variant_types`.`id`')),
                            model: orderProductVariantSubTypes,
                            include: [
                              {
                                attributes: [
                                  ['id', "productVariantSubTypeID"],
                                  ['variantType', "extraItem"],
                                  'price',
                                ],
                                model: productVariantSubTypes,
                              }
                            ]
                          }
                        ]
                      }
                    ],
                  }
                ]
              },
              {
                required: true,
                model: user,
                attributes: ['id', 'fullName', 'mobile', 'email']
              },
              {
                model: coupons,
                attributes: ['id', 'code', 'name', 'description']
              },
              {
                model: orderTableNumber,
                attributes: ['tableCode']
              },
              {
                attributes: [
                  'id','name', 'percentage', "taxID", "amount"
                ],
                model: orderTax,
              },
              {
                attributes: [
                  'id','name', 'percentage', "taxID", "amount"
                ],
                model: orderRefundTax,
              },
            ],
            order: [['createdAt', 'ASC']],
            distinct: true,
            duplicating: false
          });
      }
      if (pickupClause) {
        pickupServiceOrder = await order
          .findAll({
            where: [{
              orderServiceType: 'PICKUP'
            }, {
              isDeleted: 'No',
              barID: barID,
              isCanceled: 'No',
              paymentStatus: 'received',
              [Op.or]: [
                {
                  orderStatus: 'New'
                },
                {
                  orderStatus: 'Preparing'
                }
              ],
            }],
            attributes: [...orderAttributes, 'pickupCode'],
            include: [
              {
                required: true,
                attributes: itemAttributes,
                model: orderItems,
                include: [
                  {
                    where: { categoryID: pickupCategory },
                    attributes: productAttributes,
                    model: product,
                    include: [
                      {
                        attributes: [
                          'id',
                          'description',
                          'address'
                        ],
                        model: pickupLocation,
                      }
                    ]
                  },
                  {
                    attributes: [
                      'id',
                      'orderItemID',
                      'productExtrasID',
                      'price'
                    ],
                    model: orderItemExtras,
                    include: [
                      {
                        attributes: [
                          'id',
                          'extraItem'
                        ],
                        model: productExtras,
                      }
                    ]
                  },
                  {
                    attributes: [
                      'id',
                      'orderItemID',
                      'productVariantsID',
                      'price'
                    ],
                    model: orderItemVariants,
                    include: [
                      {
                        attributes: [
                          'id',
                          'variantType'
                        ],
                        model: productVariants,
                      }
                    ]
                  },
                  {
                    attributes: [
                      ['id', "orderProductVariantTypeID"],
                      'orderItemID',
                    ],
                    where: Sequelize.where(Sequelize.col('`order_items`.`id`'), Sequelize.col('`order_items->order_product_variant_types`.`orderItemID`')),
                    model: orderProductVariantTypes,
                    required: false,
                    include: [
                      {
                        attributes: [
                          ['id', "productVariantTypeID"],
                          'label',
                        ],
                        model: productVariantTypes,
                        required: true,
                        include: [
                          {
                            attributes: [
                              ['id', "orderProductVariantSubTypeID"],
                              'orderItemID',
                            ],
                            where: Sequelize.where(Sequelize.col('`order_items->order_product_variant_types->product_variant_type->order_product_variant_sub_type`.`orderProductVariantTypeID`'), Sequelize.col('`order_items->order_product_variant_types`.`id`')),
                            model: orderProductVariantSubTypes,
                            include: [
                              {
                                attributes: [
                                  ['id', "productVariantSubTypeID"],
                                  ['variantType', "extraItem"],
                                  'price',
                                ],
                                model: productVariantSubTypes,
                              }
                            ]
                          }
                        ]
                      }
                    ],
                  }
                ]
              },
              {
                required: true,
                model: user,
                attributes: ['id', 'fullName', 'mobile', 'email']
              },
              {
                model: coupons,
                attributes: ['id', 'code', 'name', 'description']
              },
              {
                attributes: [
                  'id','name', 'percentage', "taxID", "amount"
                ],
                model: orderTax,
              },
              {
                attributes: [
                  'id','name', 'percentage', "taxID", "amount"
                ],
                model: orderRefundTax,
              },
            ],
            order: [['createdAt', 'ASC']],
            distinct: true,
            duplicating: false
          });
      }

      newOrder = await Promise.all([tableServiceOrder, pickupServiceOrder])
        .then((modelReturn) => modelReturn.flat())
      newOrder = newOrder.filter(finalResult => finalResult !== undefined)

      let pickupTableOrder;
      let pickupPickupOrder;

      if (pickupClause)
        pickupPickupOrder = await order
          .findAll({
            where: [{
              orderServiceType: 'PICKUP',
              orderStatus: 'Pickup'
            }, {
              isDeleted: 'No',
              barID: barID,
              isCanceled: 'No',
              paymentStatus: 'received',
            }],
            having: havingClauseOrder,
            attributes: [...orderAttributes, 'pickupCode'],
            include: [
              {
                required: true,
                attributes: itemAttributes,
                model: orderItems,
                include: [
                  {
                    where: { categoryID: pickupCategory },
                    attributes: productAttributes,
                    model: product,
                    include: [
                      {
                        attributes: [
                          'id',
                          'description',
                          'address'
                        ],
                        model: pickupLocation,
                      }
                    ]
                  },
                  {
                    attributes: [
                      'id',
                      'orderItemID',
                      'productExtrasID',
                      'price'
                    ],
                    model: orderItemExtras,
                    include: [
                      {
                        attributes: [
                          'id',
                          'extraItem'
                        ],
                        model: productExtras,
                      }
                    ]
                  },
                  {
                    attributes: [
                      'id',
                      'orderItemID',
                      'productVariantsID',
                      'price'
                    ],
                    model: orderItemVariants,
                    include: [
                      {
                        attributes: [
                          'id',
                          'variantType'
                        ],
                        model: productVariants,
                      }
                    ]
                  },
                  {
                    attributes: [
                      ['id', "orderProductVariantTypeID"],
                      'orderItemID',
                    ],
                    where: Sequelize.where(Sequelize.col('`order_items`.`id`'), Sequelize.col('`order_items->order_product_variant_types`.`orderItemID`')),
                    model: orderProductVariantTypes,
                    required: false,
                    include: [
                      {
                        attributes: [
                          ['id', "productVariantTypeID"],
                          'label',
                        ],
                        model: productVariantTypes,
                        required: true,
                        include: [
                          {
                            attributes: [
                              ['id', "orderProductVariantSubTypeID"],
                              'orderItemID',
                            ],
                            where: Sequelize.where(Sequelize.col('`order_items->order_product_variant_types->product_variant_type->order_product_variant_sub_type`.`orderProductVariantTypeID`'), Sequelize.col('`order_items->order_product_variant_types`.`id`')),
                            model: orderProductVariantSubTypes,
                            include: [
                              {
                                attributes: [
                                  ['id', "productVariantSubTypeID"],
                                  ['variantType', "extraItem"],
                                  'price',
                                ],
                                model: productVariantSubTypes,
                              }
                            ]
                          }
                        ]
                      }
                    ],
                  }
                ]
              },
              {
                required: true,
                model: user,
                attributes: ['id', 'fullName', 'mobile', 'email']
              },
              {
                model: coupons,
                attributes: ['id', 'code', 'name', 'description']
              },
              {
                attributes: [
                  'id','name', 'percentage', "taxID", "amount"
                ],
                model: orderTax,
              },
              {
                attributes: [
                  'id','name', 'percentage', "taxID", "amount"
                ],
                model: orderRefundTax,
              },
            ],
            order: [['updatedAt', 'ASC']],
            distinct: true,
            duplicating: false
          });
      if (tableClause)
        pickupTableOrder = await order
          .findAll({
            where: [{
              orderServiceType: 'TABLE',
              orderStatus: 'Pickup'
            }, {
              isDeleted: 'No',
              barID: barID,
              isCanceled: 'No',
              paymentStatus: 'received',
            }],
            having: havingClauseOrder,
            attributes: orderAttributes,
            include: [
              {
                required: true,
                attributes: itemAttributes,
                model: orderItems,
                include: [
                  {
                    where: { categoryID: tableCategory },
                    attributes: productAttributes,
                    model: product,
                    include: [
                      {
                        attributes: [
                          'id',
                          'description',
                          'address'
                        ],
                        model: pickupLocation,
                      }
                    ]
                  },
                  {
                    attributes: [
                      'id',
                      'orderItemID',
                      'productExtrasID',
                      'price'
                    ],
                    model: orderItemExtras,
                    include: [
                      {
                        attributes: [
                          'id',
                          'extraItem'
                        ],
                        model: productExtras,
                      }
                    ]
                  },
                  {
                    attributes: [
                      'id',
                      'orderItemID',
                      'productVariantsID',
                      'price'
                    ],
                    model: orderItemVariants,
                    include: [
                      {
                        attributes: [
                          'id',
                          'variantType'
                        ],
                        model: productVariants,
                      }
                    ]
                  },
                  {
                    attributes: [
                      ['id', "orderProductVariantTypeID"],
                      'orderItemID',
                    ],
                    where: Sequelize.where(Sequelize.col('`order_items`.`id`'), Sequelize.col('`order_items->order_product_variant_types`.`orderItemID`')),
                    model: orderProductVariantTypes,
                    required: false,
                    include: [
                      {
                        attributes: [
                          ['id', "productVariantTypeID"],
                          'label',
                        ],
                        model: productVariantTypes,
                        required: true,
                        include: [
                          {
                            attributes: [
                              ['id', "orderProductVariantSubTypeID"],
                              'orderItemID',
                            ],
                            where: Sequelize.where(Sequelize.col('`order_items->order_product_variant_types->product_variant_type->order_product_variant_sub_type`.`orderProductVariantTypeID`'), Sequelize.col('`order_items->order_product_variant_types`.`id`')),
                            model: orderProductVariantSubTypes,
                            include: [
                              {
                                attributes: [
                                  ['id', "productVariantSubTypeID"],
                                  ['variantType', "extraItem"],
                                  'price',
                                ],
                                model: productVariantSubTypes,
                              }
                            ]
                          }
                        ]
                      }
                    ],
                  }
                ]
              },
              {
                required: true,
                model: user,
                attributes: ['id', 'fullName', 'mobile', 'email']
              },
              {
                model: coupons,
                attributes: ['id', 'code', 'name', 'description']
              },
              {
                model: orderTableNumber,
                attributes: ['tableCode']
              },
              {
                attributes: [
                  'id','name', 'percentage', "taxID", "amount"
                ],
                model: orderTax,
              },
              {
                attributes: [
                  'id','name', 'percentage', "taxID", "amount"
                ],
                model: orderRefundTax,
              },
            ],
            order: [['updatedAt', 'ASC']],
            distinct: true,
            duplicating: false
          });

      pickupOrder = await Promise.all([pickupPickupOrder, pickupTableOrder])
        .then((modelReturn) => modelReturn.flat())
      pickupOrder = pickupOrder.filter(finalResult => finalResult !== undefined)

    }
    const today = moment(new Date()).format('YYYY-MM-DD')
    var todayEarningWhereClause = {
      isDeleted: 'No',
      barID: barID,
      paymentStatus: 'received',
    }
    let todayEarning = await order.findAll({
      attributes: [
        [
          Sequelize.fn('date_format', Sequelize.col('orderDate'), '%Y-%m-%d'),
          'oDate'
        ],
        [sequelize.literal("round(sum(total - (total * 2.9/ 100 + 0.31) - (total * 0.25 / 100 + 0.25) - transactionFee),2)"), 'amount'],
        //  remove the case if needed: this was added to cover the 2 day anomaly that happened due to 2.9% stripe fee + payout fee incorporation after second fee change was completed
      ],
      group: ['oDate'],
      where: todayEarningWhereClause,
      having: {
        oDate: {
          $eq: sequelize.literal("'" + today + "'")
        }
      }
    })

    if (todayEarning[0]) {
      todayEarning = todayEarning[0].toJSON();
    }
    const barDetails = await bar
      .findOne({
        attributes: [
          'id',
          'serviceType',
          'attachedPosConfig',
          'avatar',
          'docketStatus'
        ],
        where: { id: barID },
      })

    dashboardOrder.newOrder = newOrder
    dashboardOrder.pickupOrder = pickupOrder
    dashboardOrder.todayEarnings = (todayEarning && todayEarning.amount) ? todayEarning.amount : 0
    dashboardOrder.barDetails = barDetails

    res.status(200).send({
      success: 1,
      message: 'ORDER LIST RETRIEVED SUCCESSFULLY!',
      data: dashboardOrder
    })
  } catch (error) {
    console.log(error)
    res.status(500).send({
      success: 0,
      message: 'error'
    })
  }
}

exports.barOrderListNew = async (req, res) => {
  try {
    const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    const barID = sessionData.barID;
   
      const barAndAccess = await bar.findOne({
        where: { id: barID },
        include: [
          {
            model: barAccessToken,
            required: true,
            where: {
              barID: barID,
              accessToken: req.headers.accesstoken,
            },
            attributes: ["subCategoryIDs"],
          },
          {
            model: operatingHours,
            attributes: ["id", "weekDay", "openingHours", "closingHours", "isClosed"],
            required: false,
            where: { isClosed: 0 },
          },
        ],
      });
      
    const getUserCategory = barAndAccess?.bar_accesstokens && barAndAccess?.bar_accesstokens.length
      ? barAndAccess.bar_accesstokens[0]?.subCategoryIDs
        ? barAndAccess.bar_accesstokens[0]?.subCategoryIDs.split(",")
        : []
      : [];

    const whereClauseProduct = [];
    const dashboardOrder = {}
    // const filterType = ['1','2','3','4']
    // let pickupClause = false
    // let pickupCategory = [-1]
    // let tableClause = false
    // let tableCategory = [-1]
    // let whereClauseCategoryOrder = []
    let pickupOrder = []
    let newOrder = []
    // let havingClauseOrder = [
    //   {
    //     totalCancelItem: {
    //       [Op.gt]: 0
    //     }
    //   }
    // ];
    
    if(getUserCategory.length > 0) {
      whereClauseProduct.push({
        subCategoryID : getUserCategory
      });
    }
    
    // if (filterType !== '') {
      // filterType.map(
      //   filterReceived => {
      //     switch (filterReceived) {
      //       case "1":
      //         pickupClause = true
      //         pickupCategory.push(1)
      //         break;
      //       case "2":
      //         pickupClause = true
      //         pickupCategory.push(2)
      //         break;
      //       case "3":
      //         tableClause = true
      //         tableCategory.push(1)
      //         break;
      //       case '4':
      //         tableClause = true
      //         tableCategory.push(2)
      //         break;
      //       default:
      //         break
      //     }
      //   })
      const orderAttributes = [
        'id',
        'orderNo',
        // [Sequelize.literal(`IF(orderServiceType='TABLE', tableCode, pickupCode)`), 'pickupCode'],
        'subTotal',
        'transactionFee',
        'refundTransactionFee',
        'tax',
        'total',
        'orderDate',
        'orderStatus',
        'refundStatus',
        'promocode_id',
        'promocode_amount',
        'promocode_discount',
        'userID',
        'barID',
        'posOrderStatus',
        'orderServiceType',
        'createdAt',
        'docketPrintingStatus',
        'pickupCode',
        // [
        //   Sequelize.literal(
        //     '(select count(order_items.id) from order_items WHERE order_items.orderID = `orders`.id AND order_items.isCanceled="No")'
        //   ),
        //   'totalCancelItem'
        // ],
      ]
      const itemAttributes = [
        'id',
        'orderID',
        'productID',
        'price',
        'quantity',
        'specialRequest',
        'isCanceled',
        'refundAmount',
        'refundedQuantity',
        'waitTime',
        'orderStatus',
        'PreparingStartTime',
        'ReadyTime',
        'PickedupTime',
        [Sequelize.literal(`(ADDTIME(order_items.createdAt, waitTime)) `), 'expectedTime'],
        [Sequelize.literal(`(select address from product INNER JOIN pickup_location ON product.pickupLocationID=pickup_location.id where product.id = order_items.productID ) `), 'pickupLocation'],
        [Sequelize.literal(`(select subCategoryID from product where product.id = order_items.productID ) `), 'subCategoryID'],
      ]
      const productAttributes = [
        'id',
        'name',
        'categoryID',
        'description',
        'avatar',
        'subCategoryID'
      ]
      newOrder = await order
        .findAll({
          where: [{
            isDeleted: 'No',
            paymentStatus: 'received',
            barID: barID,
            isCanceled: 'No',
            [Op.or]: [
              {
                orderStatus: 'New'
              },
              {
                orderStatus: 'Preparing'
              }
            ],
            [Op.not] : [
              {
                orderStatus: 'Intoxicated'
              },
            ]
          }],
          attributes: orderAttributes,
          include: [
            {
              required: true,
              attributes: itemAttributes,
              where: [
                {
                  [Op.or]: [
                    {
                      orderStatus: 'New'
                    },
                    {
                      orderStatus: 'Preparing'
                    }
                  ],
                  isCanceled : 'No'
                },
              ],
              model: orderItems,
              include: [
                {
                  where: [...whereClauseProduct],
                  attributes: productAttributes,
                  model: product,
                  include: [
                    {
                      attributes: [
                        'id',
                        'description',
                        'address'
                      ],
                      model: pickupLocation,
                    }
                  ]
                },
                {
                  attributes: [
                    'id',
                    'orderItemID',
                    'productExtrasID',
                    'price'
                  ],
                  model: orderItemExtras,
                  include: [
                    {
                      attributes: [
                        'id',
                        'extraItem'
                      ],
                      model: productExtras,
                    }
                  ]
                },
                {
                  attributes: [
                    'id',
                    'orderItemID',
                    'productVariantsID',
                    'price'
                  ],
                  model: orderItemVariants,
                  include: [
                    {
                      attributes: [
                        'id',
                        'variantType'
                      ],
                      model: productVariants,
                    }
                  ]
                },
                {
                  attributes: [
                    ['id', "orderProductVariantTypeID"],
                    'orderItemID',
                  ],
                  where: Sequelize.where(Sequelize.col('`order_items`.`id`'), Sequelize.col('`order_items->order_product_variant_types`.`orderItemID`')),
                  model: orderProductVariantTypes,
                  required: false,
                  include: [
                    {
                      attributes: [
                        ['id', "productVariantTypeID"],
                        'label',
                      ],
                      model: productVariantTypes,
                      required: true,
                      include: [
                        {
                          attributes: [
                            ['id', "orderProductVariantSubTypeID"],
                            'orderItemID',
                          ],
                          where: Sequelize.where(Sequelize.col('`order_items->order_product_variant_types->product_variant_type->order_product_variant_sub_type`.`orderProductVariantTypeID`'), Sequelize.col('`order_items->order_product_variant_types`.`id`')),
                          model: orderProductVariantSubTypes,
                          include: [
                            {
                              attributes: [
                                ['id', "productVariantSubTypeID"],
                                ['variantType', "extraItem"],
                                'price',
                              ],
                              model: productVariantSubTypes,
                            }
                          ]
                        }
                      ]
                    }
                  ],
                }
              ]
            },
            {
              required: true,
              model: user,
              attributes: ['id', 'fullName', 'countryCode', 'mobile', 'email']
            },
            {
              model: coupons,
              attributes: ['id', 'code', 'name', 'description']
            },
            {
              model: orderTableNumber,
              attributes: ['tableCode']
            },
            {
              attributes: [
                'id','name', 'percentage', "taxID", "amount"
              ],
              model: orderTax,
            },
            {
              attributes: [
                'id','name', 'percentage', "taxID", "amount"
              ],
              model: orderRefundTax,
            },
          ],
          order: [['createdAt', 'ASC']],
          distinct: true,
          duplicating: false,
        });

      // newOrder = await Promise.all([tableServiceOrder, pickupServiceOrder])
      //   .then((modelReturn) => modelReturn.flat())
      // newOrder = newOrder.filter(finalResult => finalResult !== undefined)

      
      pickupOrder = await order
        .findAll({
          where: [{
            orderStatus : {
              [Op.notIn]: ['Intoxicated', 'Pickedup']
            },
            isDeleted: 'No',
            barID: barID,
            isCanceled: 'No',
            paymentStatus: 'received',
          }],
          // having: havingClauseOrder,
          attributes: [...orderAttributes],
          include: [
            {
              required: true,
              attributes: itemAttributes,
              model: orderItems,
              where: {
                orderStatus: 'Pickup',
                isCanceled : 'No'
              },
              include: [
                {
                  where: [...whereClauseProduct],
                  attributes: productAttributes,
                  model: product,
                  include: [
                    {
                      attributes: [
                        'id',
                        'description',
                        'address'
                      ],
                      model: pickupLocation,
                    }
                  ]
                },
                {
                  attributes: [
                    'id',
                    'orderItemID',
                    'productExtrasID',
                    'price'
                  ],
                  model: orderItemExtras,
                  include: [
                    {
                      attributes: [
                        'id',
                        'extraItem'
                      ],
                      model: productExtras,
                    }
                  ]
                },
                {
                  attributes: [
                    'id',
                    'orderItemID',
                    'productVariantsID',
                    'price'
                  ],
                  model: orderItemVariants,
                  include: [
                    {
                      attributes: [
                        'id',
                        'variantType'
                      ],
                      model: productVariants,
                    }
                  ]
                },
                {
                  attributes: [
                    ['id', "orderProductVariantTypeID"],
                    'orderItemID',
                  ],
                  where: Sequelize.where(Sequelize.col('`order_items`.`id`'), Sequelize.col('`order_items->order_product_variant_types`.`orderItemID`')),
                  model: orderProductVariantTypes,
                  required: false,
                  include: [
                    {
                      attributes: [
                        ['id', "productVariantTypeID"],
                        'label',
                      ],
                      model: productVariantTypes,
                      required: true,
                      include: [
                        {
                          attributes: [
                            ['id', "orderProductVariantSubTypeID"],
                            'orderItemID',
                          ],
                          where: Sequelize.where(Sequelize.col('`order_items->order_product_variant_types->product_variant_type->order_product_variant_sub_type`.`orderProductVariantTypeID`'), Sequelize.col('`order_items->order_product_variant_types`.`id`')),
                          model: orderProductVariantSubTypes,
                          include: [
                            {
                              attributes: [
                                ['id', "productVariantSubTypeID"],
                                ['variantType', "extraItem"],
                                'price',
                              ],
                              model: productVariantSubTypes,
                            }
                          ]
                        }
                      ]
                    }
                  ],
                }
              ]
            },
            {
              required: true,
              model: user,
              attributes: ['id', 'fullName', 'countryCode', 'mobile', 'email']
            },
            {
              model: coupons,
              attributes: ['id', 'code', 'name', 'description']
            },
            {
              model: orderTableNumber,
              attributes: ['tableCode']
            },
            {
              attributes: [
                'id','name', 'percentage', "taxID", "amount"
              ],
              model: orderTax,
            },
            {
              attributes: [
                'id','name', 'percentage', "taxID", "amount"
              ],
              model: orderRefundTax,
            },
          ],
          order: [['updatedAt', 'ASC']],
          distinct: true,
          duplicating: false
        });

      // pickupOrder = await Promise.all([pickupPickupOrder, pickupTableOrder])
      //   .then((modelReturn) => modelReturn.flat())
      // pickupOrder = pickupOrder.filter(finalResult => finalResult !== undefined)

    // }
    const today = moment(new Date()).format('YYYY-MM-DD')
    var todayEarningWhereClause = {
      isDeleted: 'No',
      barID: barID,
      paymentStatus: 'received',
    }
    let todayEarning = await order.findAll({
      attributes: [
        [
          Sequelize.fn('date_format', Sequelize.col('orderDate'), '%Y-%m-%d'),
          'oDate'
        ],
        [sequelize.literal("round(sum(total - (total * 2.9/ 100 + 0.31) - (total * 0.25 / 100 + 0.25) - transactionFee),2)"), 'amount'],
        //  remove the case if needed: this was added to cover the 2 day anomaly that happened due to 2.9% stripe fee + payout fee incorporation after second fee change was completed
      ],
      group: ['oDate'],
      where: todayEarningWhereClause,
      having: {
        oDate: {
          $eq: sequelize.literal("'" + today + "'")
        }
      }
    })

    if (todayEarning[0]) {
      todayEarning = todayEarning[0].toJSON();
    }
    
    if(barAndAccess.dataValues.posStatus && barAndAccess.dataValues.posStatus === '1'){
      var whereClauseSubCategory = {
        categoryID: '-1',
        isDeleted: 'No'
      }
    }else{
      var whereClauseSubCategory = {
        [Op.not]: [{ categoryID: '-1' }],
        isDeleted: 'No'
      }
    }
    let currentDay = moment().tz('Australia/Perth').isoWeekday()
    currentDay = currentDay - 1;
    const subCategoryList = await subCategory
      .findAll({
        attributes: [
          [sequelize.literal(`(SELECT IF(IA.status = '1' and CAST('${moment().tz('Australia/Perth').format('HH:mm:ss')}' AS TIME) between CAST(IA.activeHours AS TIME) and CAST(IA.inActiveHours AS TIME), 1, 0)isActive FROM itemActiveHours as IA WHERE sub_category.id = IA.subCategoryID AND IA.barID = ${barID} AND IA.weekDay = ${currentDay} HAVING isActive = 1)`), 'operatingFlag'],
        ],
        where: whereClauseSubCategory
      });
    let barCategoryIsOpen = 0;
    subCategoryList.map(cat1 =>  {
      if (cat1.dataValues.operatingFlag == 1) {
        barCategoryIsOpen = 1;
      }
    })
    let barIsOpen = commonFunction.checkBarIsOpen(barAndAccess.operating_hours);
    let pauseStartTime = null;
    let pauseEndTime = null;
    let pauseOrderStatus = 0;
    if(barAndAccess.dataValues.pauseOrderStartTime){
      let endTime = moment.utc(barAndAccess.dataValues.pauseOrderStartTime).add(barAndAccess.dataValues.pauseOrderLimit,'minutes');
      if(endTime.isAfter(moment.utc())){
        pauseStartTime = barAndAccess.dataValues.pauseOrderStartTime;
        pauseEndTime = endTime;
        pauseOrderStatus = 1;
      }
    }
    let newOrdersObj = newOrder.map((order) => {
      let newOrderItems = groupByOrderItems(order.order_items, order.orderServiceType, order.refundStatus); // Group By order items
      // delete order.dataValues.order_items; // Delete old key
      order.dataValues['order_items_group'] = newOrderItems; // Add new key to object
      return order
    })
    
    dashboardOrder.newOrder = newOrdersObj

    let pickupOrderObj = pickupOrder.map((order) => {
      let newOrderItems = groupByOrderItems(order.order_items, order.orderServiceType, order.refundStatus); // Group By order items
      // delete order.dataValues.order_items; // Delete old key
      order.dataValues['order_items_group'] = newOrderItems; // Add new key to object
      return order
    })

    dashboardOrder.pickupOrder = pickupOrderObj

    dashboardOrder.todayEarnings = (todayEarning && todayEarning.amount) ? todayEarning.amount : 0
    dashboardOrder.barDetails = barAndAccess
    dashboardOrder.barDetails.dataValues.barIsOpen = barIsOpen;
    dashboardOrder.barDetails.dataValues.pauseStartTime = pauseStartTime;
    dashboardOrder.barDetails.dataValues.pauseEndTime = pauseEndTime;
    dashboardOrder.barDetails.dataValues.pauseOrderStatus = pauseOrderStatus;
    dashboardOrder.barDetails.dataValues.barCategoryIsOpen = barCategoryIsOpen;

    res.status(200).send({
      success: 1,
      message: 'ORDER LIST RETRIEVED SUCCESSFULLY!',
      data: dashboardOrder
    })
  } catch (error) {
    console.log(error)
    res.status(500).send({
      success: 0,
      message: error
    })
  }
}

exports.barOrderListCount = async (req, res) => {
  console.log('barOrderListCount.......');
  try {
    const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    const barID = sessionData.barID;

    const barAndAccess = await bar.findOne({
      where: { id: barID },
      include: [
        {
          model: barAccessToken,
          required: true,
          where: {
            barID: barID,
            accessToken: req.headers.accesstoken,
          },
          attributes: ["subCategoryIDs"],
        },
        {
          model: operatingHours,
          attributes: ["id", "weekDay", "openingHours", "closingHours", "isClosed"],
          required: false,
          where: { isClosed: 0 },
        },
      ],
    });

    const getUserCategory = barAndAccess?.bar_accesstokens && barAndAccess?.bar_accesstokens.length
      ? barAndAccess.bar_accesstokens[0]?.subCategoryIDs
        ? barAndAccess.bar_accesstokens[0]?.subCategoryIDs.split(",")
        : []
      : [];

    const whereClauseProduct = [];
    const dashboardOrder = {}
    let pickupOrder = []
    let newOrder = []
    
    if(getUserCategory.length > 0) {
      whereClauseProduct.push({
        subCategoryID : getUserCategory
      });
    }

    const orderAttributes = [
      'id',
      'orderNo',
      'subTotal',
      'transactionFee',
      'tax',
      'total',
      'orderDate',
      'orderStatus',
      'refundStatus',
      'promocode_id',
      'promocode_amount',
      'promocode_discount',
      'userID',
      'barID',
      'posOrderStatus',
      'orderServiceType',
      'createdAt',
      'docketPrintingStatus',
      'pickupCode',
    ]
    newOrder = await order
      .findAll({
        where: [{
          isDeleted: 'No',
          paymentStatus: 'received',
          barID: barID,
          isCanceled: 'No',
          [Op.or]: [
            {
              orderStatus: 'New'
            },
            {
              orderStatus: 'Preparing'
            }
          ],
          [Op.not] : [
            {
              orderStatus: 'Intoxicated'
            },
          ]
        }],
        attributes: orderAttributes,
        include: [
          {
            required: true,
            where: [
              {
                [Op.or]: [
                  {
                    orderStatus: 'New'
                  },
                  {
                    orderStatus: 'Preparing'
                  }
                ],
                isCanceled : 'No'
              },
            ],
            model: orderItems,
            include: [
              {
                where: [...whereClauseProduct],
                model: product,
              }
            ]
          },
          {
            required: true,
            model: user,
            attributes: ['id', 'fullName', 'countryCode', 'mobile', 'email']
          }
        ],
        order: [['createdAt', 'ASC']],
        distinct: true,
        duplicating: false,
      });

    pickupOrder = await order
      .findAll({
        where: [{
          orderStatus : {
            [Op.notIn]: ['Intoxicated', 'Pickedup']
          },
          isDeleted: 'No',
          barID: barID,
          isCanceled: 'No',
          paymentStatus: 'received',
        }],
        attributes: [...orderAttributes],
        include: [
          {
            required: true,
            model: orderItems,
            where: {
              orderStatus: 'Pickup',
              isCanceled : 'No'
            },
            include: [
              {
                where: [...whereClauseProduct],
                model: product
              }
            ]
          },
          {
            required: true,
            model: user,
            attributes: ['id', 'fullName', 'countryCode', 'mobile', 'email']
          }
        ],
        order: [['updatedAt', 'ASC']],
        distinct: true,
        duplicating: false
      });
      
    if (barAndAccess.dataValues.posStatus && barAndAccess.dataValues.posStatus === '1') {
      var whereClause = {
        categoryID: '-1',
        isDeleted: 'No'
      }
    } else {
      var whereClause = {
        [Op.not]: [{ categoryID: '-1' }],
        isDeleted: 'No'
      }
    }
    let currentDay = moment().tz('Australia/Perth').isoWeekday()
    currentDay = currentDay - 1;
    const subCategoryList = await subCategory.findAll({
      attributes: [
        [
          sequelize.literal(`
            EXISTS (
              SELECT 1
              FROM itemActiveHours AS IA
              WHERE sub_category.id = IA.subCategoryID
                AND IA.barID = :barID
                AND IA.weekDay = :weekDay
                AND IA.status = '1'
                AND CAST(:currentTime AS TIME)
                  BETWEEN CAST(IA.activeHours AS TIME)
                  AND CAST(IA.inActiveHours AS TIME)
            )
          `),
          'operatingFlag',
        ]
      ],
      replacements: {
        barID: barID,
        weekDay: currentDay,
        currentTime: moment().tz('Australia/Perth').format('HH:mm:ss')
      },
      where: whereClause
    });

    let barCategoryIsOpen = 0;

    subCategoryList.map(cat1 => {
      if (cat1.dataValues.operatingFlag == 1) {
        barCategoryIsOpen = 1;
      }
    })
    let barIsOpen = commonFunction.checkBarIsOpen(barAndAccess.operating_hours);
    let pauseStartTime = null;
    let pauseEndTime = null;
    let pauseOrderStatus = 0;
    if (barAndAccess.dataValues.pauseOrderStartTime) {
      let endTime = moment.utc(barAndAccess.dataValues.pauseOrderStartTime).add(barAndAccess.dataValues.pauseOrderLimit, 'minutes');
      if (endTime.isAfter(moment.utc())) {
        pauseStartTime = barAndAccess.dataValues.pauseOrderStartTime;
        pauseEndTime = endTime;
        pauseOrderStatus = 1;
      }
    }
    res.status(200).send({
      success: 1,
      message: 'ORDER LIST COUNT RETRIEVED SUCCESSFULLY!',
      data: { 'newOrderCount': newOrder.length, 'newOrder': newOrder, 'pickupOrderCount': pickupOrder.length, 'pickupOrder': pickupOrder, 'readPopup': barAndAccess.readPopup, 'pauseOrderStatus': pauseOrderStatus, 'pauseStartTime': pauseStartTime, 'pauseEndTime': pauseEndTime, 'barIsOpen': barIsOpen, 'barCategoryIsOpen': barCategoryIsOpen }
    })
  } catch (error) {
    console.log(error)
    res.status(500).send({
      success: 0,
      message: error
    })
  }
}

exports.barOrderHistory = async (req, res) => {
  try {
    const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    const barID = sessionData.barID;
    const filterType = req.body.filter_type
    let historyPickupClause = false
    let pickupCategory = [-1]
    let historyTableClause = false
    let tableCategory = [-1]
    let finalPickupOrder = [];

    let pickupOrders = [];
    let tableOrders = [];
    const whereClauseOrder = []
    const havingClauseOrder = []
    const whereClauseRefundedItem = []
    const orderAttributes = [
      'id',
      'orderNo',
      // [Sequelize.literal(`IF(orderServiceType='TABLE', tableCode, pickupCode)`), 'pickupCode'],
      'subTotal',
      'transactionFee',
      'tax',
      'total',
      'orderDate',
      'orderStatus',
      'refundStatus',
      'promocode_id',
      'promocode_amount',
      'promocode_discount',
      'userID',
      'barID',
      'orderServiceType',
      'docketPrintingStatus',
      [
        Sequelize.literal(
          '(select count(order_items.id) from order_items WHERE order_items.orderID = `orders`.id AND order_items.isCanceled="No")'
        ),
        'totalCancelItem'
      ],
      [
        Sequelize.literal(
          '(select sum(order_tax.amount) from order_tax WHERE order_tax.orderID = `orders`.id)'
        ),
        'totalOrderTax'
      ],
      [
        Sequelize.literal(
          '(select sum(order_refund_tax.amount) from order_refund_tax WHERE order_refund_tax.orderID = `orders`.id)'
        ),
        'totalOrderRefundTax'
      ],
      'createdAt'
    ]
    if (filterType !== '') {
      filterType.split(',')
        .map(
          filterReceived => {
            switch (filterReceived) {
              case "1":
                historyPickupClause = true
                pickupCategory.push(1)
                break;
              case "2":
                historyPickupClause = true
                pickupCategory.push(2)
                break;
              case "3":
                historyTableClause = true
                tableCategory.push(1)
                break;
              case '4':
                historyTableClause = true
                tableCategory.push(2)
                break;
              default:
                break
            }
          })
      whereClauseOrder.push({
        isDeleted: 'No',
        barID: barID
      })

      if (req.body.orderType && req.body.orderType != '') {
        if (req.body.orderType == 'current') {
          whereClauseOrder.push({
            [Op.or]: [
              sequelize.where(sequelize.col('orders.orderStatus'), '=', 'New'),
              sequelize.where(sequelize.col('orders.orderStatus'), '=', 'Preparing'),
              // {
              //   orderStatus: 'Pickup'
              // }
            ],
            isCanceled: 'No',
            paymentStatus: 'received'
          })
        } else if (req.body.orderType == 'past') {
          whereClauseOrder.push(Sequelize.where(Sequelize.col('orders.orderStatus'), 'not in', [['New', 'Preparing']]))
          whereClauseOrder.push({
            isCanceled: 'No',
            paymentStatus: 'received'
          })
          // havingClauseOrder.push({
          //   totalCancelItem: {
          //     [Op.gt]: 0
          //   }
          // })
        } else if (req.body.orderType == 'refund') {
          whereClauseOrder.push({
            [Op.or]: [
              {
                refundStatus: 'Refunded'
              },
              {
                refundStatus: 'PartialRefunded'
              },
              sequelize.where(sequelize.col('orders.orderStatus'), '=', 'Intoxicated'),
            ],
            paymentStatus: 'received'
          })
          whereClauseRefundedItem.push({
            [Op.or]: [
              {
                isCanceled: 'Yes'
              },
              {
                refundedQuantity: {
                  [Op.gt]: 0
                }
              },
              sequelize.where(sequelize.col('orders.orderStatus'), '=', 'Intoxicated')
            ]
          })
        } else if (req.body.orderType == 'cancel') {
          whereClauseOrder.push({
            [Op.and]: [
              {
                isCanceled: 'Yes',
                refundStatus: 'No'
              }
            ]
          })
        } else if (req.body.orderType == 'Intoxicated') {
          whereClauseOrder.push({
            [Op.and]: [
              sequelize.where(sequelize.col('orders.orderStatus'), '=', 'Intoxicated'),
              {
                isCanceled: 'No',
                refundStatus: 'No'
              }
            ]
          })
        }
      } else {
        whereClauseOrder.push({
          [Op.or]: [
            sequelize.where(sequelize.col('orders.orderStatus'), '=', 'New'),
            sequelize.where(sequelize.col('orders.orderStatus'), '=', 'Preparing'),
            sequelize.where(sequelize.col('orders.orderStatus'), '=', 'Pickup'),
          ],
          paymentStatus: 'received'
        })
      }

      if (historyPickupClause)
        pickupOrders = order
          .findAll({
            where: [
              ...whereClauseOrder,
              { orderServiceType: 'PICKUP' }
            ],
            having: havingClauseOrder,
            attributes: [...orderAttributes, 'pickupCode'],
            include: [
              {
                required: true,
                attributes: [
                  'id',
                  'orderID',
                  'productID',
                  'price',
                  'quantity',
                  'specialRequest',
                  'isCanceled',
                  'refundAmount',
                  'refundedQuantity'
                ],
                model: orderItems,
                where: whereClauseRefundedItem,
                include: [
                  {
                    where: { categoryID: pickupCategory },
                    attributes: [
                      'id',
                      'name',
                      'categoryID',
                      'subCategoryID',
                      'description',
                      'avatar',
                      'posID',
                    ],
                    model: product,
                    include: [
                      {
                        attributes: [
                          'id',
                          'description',
                          'address'
                        ],
                        model: pickupLocation,
                      }
                    ]
                  },
                  {
                    attributes: [
                      'id',
                      'orderItemID',
                      'productExtrasID',
                      'price'
                    ],
                    model: orderItemExtras,
                    include: [
                      {
                        attributes: [
                          'id',
                          'extraItem',
                          'posID',
                        ],
                        model: productExtras,
                      }
                    ]
                  },
                  {
                    attributes: [
                      'id',
                      'orderItemID',
                      'productVariantsID',
                      'price'
                    ],
                    model: orderItemVariants,
                    include: [
                      {
                        attributes: [
                          'id',
                          'variantType'
                        ],
                        model: productVariants,
                      }
                    ]
                  },
                  {
                    attributes: [
                      ['id', "orderProductVariantTypeID"],
                      'orderItemID',
                    ],
                    where: Sequelize.where(Sequelize.col('`order_items`.`id`'), Sequelize.col('`order_items->order_product_variant_types`.`orderItemID`')),
                    model: orderProductVariantTypes,
                    required: false,
                    include: [
                      {
                        attributes: [
                          ['id', "productVariantTypeID"],
                          'label',
                        ],
                        model: productVariantTypes,
                        required: true,
                        include: [
                          {
                            attributes: [
                              ['id', "orderProductVariantSubTypeID"],
                              'orderItemID',
                            ],
                            where: Sequelize.where(Sequelize.col('`order_items->order_product_variant_types->product_variant_type->order_product_variant_sub_type`.`orderProductVariantTypeID`'), Sequelize.col('`order_items->order_product_variant_types`.`id`')),
                            model: orderProductVariantSubTypes,
                            include: [
                              {
                                attributes: [
                                  ['id', "productVariantSubTypeID"],
                                  ['variantType', "extraItem"],
                                  'price',
                                ],
                                model: productVariantSubTypes,
                              }
                            ]
                          }
                        ]
                      }
                    ],
                  }
                ]
              },
              {
                required: true,
                model: user,
                attributes: ['id', 'fullName', 'mobile', 'email']
              },
              {
                model: coupons,
                attributes: ['id', 'code', 'name', 'description']
              },
              {
                attributes: [
                  'id','name', 'percentage', "taxID", "amount"
                ],
                model: orderTax,
              },
              {
                attributes: [
                  'id','name', 'percentage', "taxID", "amount"
                ],
                model: orderRefundTax,
              },
            ],
            order: [['createdAt', 'DESC']],
            distinct: true,
            duplicating: false
          })

      if (historyTableClause)
        tableOrders = order
          .findAll({
            where: [
              whereClauseOrder,
              { orderServiceType: 'TABLE' }
            ],
            having: havingClauseOrder,
            attributes: orderAttributes,
            include: [
              {
                required: true,
                attributes: [
                  'id',
                  'orderID',
                  'productID',
                  'price',
                  'quantity',
                  'specialRequest',
                  'isCanceled',
                  'refundAmount',
                  'refundedQuantity'
                ],
                model: orderItems,
                where: whereClauseRefundedItem,
                include: [
                  {
                    where: { categoryID: tableCategory },
                    attributes: [
                      'id',
                      'name',
                      'categoryID',
                      'subCategoryID',
                      'description',
                      'avatar'
                    ],
                    model: product,
                    include: [
                      {
                        attributes: [
                          'id',
                          'description',
                          'address'
                        ],
                        model: pickupLocation,
                      }
                    ]
                  },
                  {
                    attributes: [
                      'id',
                      'orderItemID',
                      'productExtrasID',
                      'price'
                    ],
                    model: orderItemExtras,
                    include: [
                      {
                        attributes: [
                          'id',
                          'extraItem'
                        ],
                        model: productExtras,
                      }
                    ]
                  },
                  {
                    attributes: [
                      'id',
                      'orderItemID',
                      'productVariantsID',
                      'price'
                    ],
                    model: orderItemVariants,
                    include: [
                      {
                        attributes: [
                          'id',
                          'variantType'
                        ],
                        model: productVariants,
                      }
                    ]
                  },
                  {
                    attributes: [
                      ['id', "orderProductVariantTypeID"],
                      'orderItemID',
                    ],
                    where: Sequelize.where(Sequelize.col('`order_items`.`id`'), Sequelize.col('`order_items->order_product_variant_types`.`orderItemID`')),
                    model: orderProductVariantTypes,
                    required: false,
                    include: [
                      {
                        attributes: [
                          ['id', "productVariantTypeID"],
                          'label',
                        ],
                        model: productVariantTypes,
                        required: true,
                        include: [
                          {
                            attributes: [
                              ['id', "orderProductVariantSubTypeID"],
                              'orderItemID',
                            ],
                            where: Sequelize.where(Sequelize.col('`order_items->order_product_variant_types->product_variant_type->order_product_variant_sub_type`.`orderProductVariantTypeID`'), Sequelize.col('`order_items->order_product_variant_types`.`id`')),
                            model: orderProductVariantSubTypes,
                            include: [
                              {
                                attributes: [
                                  ['id', "productVariantSubTypeID"],
                                  ['variantType', "extraItem"],
                                  'price',
                                ],
                                model: productVariantSubTypes,
                              }
                            ]
                          }
                        ]
                      }
                    ],
                  }
                ]
              },
              {
                required: true,
                model: user,
                attributes: ['id', 'fullName', 'mobile', 'email']
              },
              {
                model: coupons,
                attributes: ['id', 'code', 'name', 'description']
              },
              {
                model: orderTableNumber,
                attributes: ['tableCode']
              },
              {
                attributes: [
                  'id','name', 'percentage', "taxID", "amount"
                ],
                model: orderTax,
              },
              {
                attributes: [
                  'id','name', 'percentage', "taxID", "amount"
                ],
                model: orderRefundTax,
              },
            ],
            order: [['id', 'DESC']],
            distinct: true,
            duplicating: false
          })

      finalPickupOrder = await Promise.all([pickupOrders, tableOrders])
        .then((modelReturn) => modelReturn.flat());
      finalPickupOrder = finalPickupOrder.filter(finalResult => {
        if(finalResult !== undefined) {
          
          if(req.body.orderType == 'past' && (finalResult.dataValues.totalCancelItem > 0 || (finalResult.dataValues.totalOrderTax && finalResult.dataValues.totalOrderTax > finalResult.dataValues.totalOrderRefundTax))) {
            return finalResult;  
          } else {
            return finalResult;
          }
        }
      });
    }
    if (finalPickupOrder.length > 0) {
      res.status(200).send({
        success: 1,
        message: 'order list retrieve successfully!',
        data: finalPickupOrder
      })
    } else {
      res.status(200).json({
        success: 1,
        message: 'No Results Found',
        data: []
      })
    }
  } catch (error) {
    console.log(error)
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.barOrderDetail = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  try {
    var whereClauseOrder = []
    whereClauseOrder.push({
      isDeleted: 'No'
    })
    whereClauseOrder.push({
      barID: barID
    })
    whereClauseOrder.push({
      id: req.body.id
    })

    order
      .findAll({
        where: whereClauseOrder,
        attributes: [
          'id',
          'orderNo',
          'pickupCode',
          // [Sequelize.literal(`IF(orderServiceType='TABLE', tableCode, pickupCode)`), 'pickupCode'],
          'subTotal',
          'transactionFee',
          'refundTransactionFee',
          'tax',
          'total',
          'orderDate',
          'orderStatus',
          'orderServiceType',
          'promocode_id',
          'promocode_amount',
          'promocode_discount',
          'userID',
          'barID',
          'createdAt'
        ],
        include: [
          {
            attributes: [
              'id',
              'orderID',
              'productID',
              'price',
              'quantity',
              'specialRequest',
              'isCanceled',
              'refundAmount',
              'refundedQuantity',
              'waitTime',
              'orderStatus',
              'PreparingStartTime',
              'ReadyTime',
              'PickedupTime',
              [Sequelize.literal(`(ADDTIME(order_items.createdAt, waitTime)) `), 'expectedTime'],
              [Sequelize.literal(`(select address from product INNER JOIN pickup_location ON product.pickupLocationID=pickup_location.id where product.id = order_items.productID ) `), 'pickupLocation'],
              [Sequelize.literal(`(select subCategoryID from product where product.id = order_items.productID ) `), 'subCategoryID'],
            ],
            model: orderItems,
            include: [
              {
                attributes: [
                  'id',
                  'name',
                  'description',
                  'avatar'
                ],
                model: product,
                include: [
                  {
                    attributes: [
                      'id',
                      'description',
                      'address'
                    ],
                    model: pickupLocation,
                  }
                ]
              },
              {
                attributes: [
                  'id',
                  'orderItemID',
                  'productExtrasID',
                  'price'
                ],
                model: orderItemExtras,
                include: [
                  {
                    attributes: [
                      'id',
                      'extraItem'
                    ],
                    model: productExtras,
                  }
                ]
              },
              {
                attributes: [
                  'id',
                  'orderItemID',
                  'productVariantsID',
                  'price'
                ],
                model: orderItemVariants,
                include: [
                  {
                    attributes: [
                      'id',
                      'variantType'
                    ],
                    model: productVariants,
                  }
                ]
              },
              {
                attributes: [
                  ['id', "orderProductVariantTypeID"],
                  'orderItemID',
                ],
                where: Sequelize.where(Sequelize.col('`order_items`.`id`'), Sequelize.col('`order_items->order_product_variant_types`.`orderItemID`')),
                model: orderProductVariantTypes,
                required: false,
                include: [
                  {
                    attributes: [
                      ['id', "productVariantTypeID"],
                      'label',
                    ],
                    model: productVariantTypes,
                    required: true,
                    include: [
                      {
                        attributes: [
                          ['id', "orderProductVariantSubTypeID"],
                          'orderItemID',
                        ],
                        where: Sequelize.where(Sequelize.col('`order_items->order_product_variant_types->product_variant_type->order_product_variant_sub_type`.`orderProductVariantTypeID`'), Sequelize.col('`order_items->order_product_variant_types`.`id`')),
                        model: orderProductVariantSubTypes,
                        include: [
                          {
                            attributes: [
                              ['id', "productVariantSubTypeID"],
                              ['variantType', "extraItem"],
                              'price',
                            ],
                            model: productVariantSubTypes,
                          }
                        ]
                      }
                    ]
                  }
                ],
              }
            ]
          },
          {
            required: true,
            model: user,
            attributes: ['id', 'fullName', 'mobile', 'email']
          },
          {
            model: coupons,
            attributes: ['id', 'code', 'name', 'description']
          },
          {
            model: orderTableNumber,
            attributes: ['tableCode']
          },
          {
            attributes: [
              'id','name', 'percentage', "taxID", "amount"
            ],
            model: orderTax,
          },
          {
            attributes: [
              'id','name', 'percentage', "taxID", "amount"
            ],
            model: orderRefundTax,
          },
        ]
      }).then(order => {
        if (order) {
          let newOrderItems = groupByOrderItems(order[0].order_items, order[0].orderServiceType, order[0].refundStatus); // Group By order items
          // delete order[0].dataValues.order_items; // Delete old key
          order[0].dataValues['order_items_group'] = newOrderItems; // Add new key to object
          res.status(200).send({
            success: 1,
            message: 'order detail retrive successfully!',
            data: order[0]
          })
        } else {
          res.status(200).json({
            success: 0,
            message: 'No Results Found',
            data: {}
          })
        }
      }).error(function (err) {
        res.status(200).json({
          success: 0,
          message: err.message,
        })
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.getFees = async (req, res, next) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var userID = sessionData.userID
  try {
    settings.findAll()
      .then(feesData => {
        res.status(200).send({
          success: 1,
          data: feesData,
          message: 'fees list retrive successfully'
        })
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.getPromocode = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  try {
    const today = moment(new Date()).format('YYYY-MM-DD')

    var whereClause = []

    whereClause.push({
      status: 'Active'
    })
    whereClause.push(
      sequelize.where(sequelize.fn('date', sequelize.col('startsAt')), '<=', today)
    )
    whereClause.push(
      sequelize.where(sequelize.fn('date', sequelize.col('expiresAt')), '>=', today)
    )
    // whereClause.push({
    //   max_uses: {
    //     [Op.lt]: sequelize.col('max_uses_user')
    //   }
    // })

    coupons
      .findAll({
        attributes: [
          'id',
          'code',
          'name',
          'description',
          'discount_amount',
          [
            Sequelize.literal(
              `(CASE is_fixed WHEN 'Yes' THEN 'Fix' ELSE 'Per' END)`
            ),
            'discount_type'
          ]
        ],
        where: whereClause,
      })
      .then(promoResponse => {
        if (promoResponse) {
          res.status(200).send({
            success: 1,
            data: promoResponse,
            message: 'Promocode list retrive successfully!'
          })
        } else {
          res.status(200).send({
            success: 0,
            message: 'Promocode not found!'
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.checkPromoCode = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var userID = sessionData.userID
  const today = moment(new Date()).format('YYYY-MM-DD')
  try {
    coupons
      .findOne({
        attributes: [
          'id',
          'barID',
          'name',
          'code',
          'discount_amount',
          'description',
          'startsAt',
          [sequelize.literal(`IF(expiresAt = '0000-00-00', null, expiresAt)`), 'expiresAt'],
          'status',
          'createdAt',
          [sequelize.literal(`IF('${today}' > (SELECT IF(expiresAt = '0000-00-00', null, expiresAt)), 1, 0)`), 'isExpired'],
        ],
        where: {
          code: req.body.code,
          status: 'Active',
          isDeleted: 'No',
          barID: req.body.barID
        },
        // 9. Promo codes - Subheading specific....  Starts
        // include: [{
        //   attributes: [
        //     'subCategoryID',
        //   ],
        //   model: couponsSubCatIds
        // }]
        // 9. Promo codes - Subheading specific....  Ends
      })
      .then(promoResponse => {
        if (promoResponse) {
          if (!promoResponse.isExpired) {
            if (promoResponse.is_fixed == 'Yes') {
              var discount_type = 'Fix'
            } else {
              var discount_type = 'Per'
            }

            promoResponse = promoResponse.toJSON(); // actually returns a plain object, not a JSON string
            promoResponse.discount_type = discount_type
            delete promoResponse.max_uses
            delete promoResponse.max_uses_user
            delete promoResponse.is_fixed
            delete promoResponse.startsAt
            delete promoResponse.expiresAt
            delete promoResponse.status
            delete promoResponse.createdAt
            delete promoResponse.updatedAt
            res.status(200).send({
              success: 1,
              data: promoResponse,
              message: 'Discount code applied successfully'
            })
          } else {
            res.status(200).send({
              success: 0,
              message: 'Discount code expired. Please try with another discount code'
            })
          }
        } else {
          res.status(200).send({
            success: 0,
            message: 'Invalid discount code, please try again.'
          })
        }
      })
  } catch (error) {
    console.log(error)
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.updateStatus = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  try {
    // const { POS } = require("./POS");
    // const posConfigured = new POS(barID)
    let { id, orderStatus } = req.body

    // await posConfigured.alterOrderStatusWithoutPOS(id, orderStatus) // used for user notifications 
    const orderDetails = await order.findByPk(id)
    if (orderDetails) {
      const updateOrderData = {
        orderStatus: orderStatus,
        updatedAt: new Date()
      }
      switch (orderStatus) {
        case 'Preparing':
          updateOrderData['PreparingStartTime'] = new Date();
          break;
        case 'Pickup':
          updateOrderData['ReadyTime'] = new Date();
          break;
        case 'Pickedup':
          updateOrderData['PickedupTime'] = new Date();
          break;
        case 'NotPickedup':
          updateOrderData['isCanceled'] = 'Yes'
          break;
      }
      await orderDetails.update(updateOrderData)
      if (orderStatus == 'Pickup') {
        try {
          order
            .findOne({
              where: {
                id: req.body.id,
                barID: barID
              }
            })
            .then(async orderDataResponse => {
              if (orderDataResponse && Object.keys(orderDataResponse).length > 0) {
                var message = 'Your Order ' + orderDataResponse.pickupCode + ' is ready to collect.'
                var notification_type = 'orderReady_pickupAlert'

                if (message != '') {
                  userNotification.create({
                    barID: barID,
                    notification_type: notification_type,
                    userID: orderDataResponse.userID,
                    dataID: req.body.id,
                    message: message,
                    createdAt: new Date()
                  })
                  commonFunction.orderStatusNotificationToUser(orderDataResponse.userID, req.body.id, notification_type, message)
                }
                res.status(200).json({
                  success: 1,
                  message: 'Order status updated',
                })
              } else {
                res.status(200).json({
                  success: 0,
                  message: 'Order not found!',
                })
              }
            })
        } catch (error) {
          res.status(200).send({
            success: 0,
            message: 'error!'
          })
        }
      }
    }else{
      res.status(200).json({
        success: 0,
        message: 'Order not found!',
      })
    }

    res.json({
      success: 1,
      message: 'success!'
    })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.updateItemStatus = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  try {
    let { orderStatus } = req.body
    let orderItemsIds = req.body.id.split(',');
    let isItemStatusUpdated  = false;
    await Promise.all(orderItemsIds.map(async orderItemId => {
      const orderItemDetails = await orderItems.findOne({
        where:{
          id: orderItemId,
        },
        attributes: [
          'id',
          'orderID',
          [Sequelize.literal(`(select orderServiceType from orders where id = order_items.orderID) `), 'orderServiceType'],
          [Sequelize.literal(`(select pickupCode from orders where id = order_items.orderID) `), 'pickupCode'],
          [Sequelize.literal(`(select barID from orders where id = order_items.orderID) `), 'barID'],
          [Sequelize.literal(`(select userID from orders where id = order_items.orderID) `), 'userID'],
        ],
        include: [
          {
            required: true,
            model: order,
            where: {
              barID: barID
            },
          }
        ]
      });
      if (orderItemDetails) {
        const updateOrderData = {
          orderStatus: orderStatus,
          updatedAt: new Date()
        }
        switch (orderStatus) {
          case 'Preparing':
            updateOrderData['PreparingStartTime'] = new Date();
            break;
          case 'Pickup':
            updateOrderData['ReadyTime'] = new Date();
            break;
          case 'Pickedup':
            updateOrderData['PickedupTime'] = new Date();
            break;
          case 'NotPickedup':
            updateOrderData['isCanceled'] = 'Yes'
            break;
        }
        await orderItemDetails.update(updateOrderData)
        isItemStatusUpdated = true;
        if (orderStatus == 'Pickup' || orderStatus == 'Pickedup' || orderStatus == 'NotPickedup') {
          orderItemWaitTimeNotifications.destroy(
            {
              where: {
                orderItemID: orderItemId
              },
            }
          )
        }
        const TotalOrderItems = await orderItems.count({
          where:{
            orderID: orderItemDetails.orderID,
            isCanceled: 'No',
            isDeleted: 'No',
          },
        });
        const TotalPickedupItems = await orderItems.count({
          where:{
            orderID: orderItemDetails.orderID,
            isDeleted: 'No',
            isCanceled: 'No',
            orderStatus: 'Pickedup'
          },
        });
        const TotalReadyItems = await orderItems.count({
          where:{
            orderID: orderItemDetails.orderID,
            isDeleted: 'No',
            isCanceled: 'No',
            orderStatus: 'Pickup'
          },
        });
        if(TotalOrderItems == TotalPickedupItems){
          await order.update({
            orderStatus: 'Pickedup',
            PickedupTime: new Date(),
            updatedAt: new Date()
          },
          {
            where: {
              id: orderItemDetails.orderID,
            }
          })
        }else if(TotalOrderItems == TotalReadyItems){
          await order.update({
            orderStatus: 'Pickup',
            ReadyTime: new Date(),
            updatedAt: new Date()
          },
          {
            where: {
              id: orderItemDetails.orderID,
            }
          })
        }
      }
    }))
    if(isItemStatusUpdated){
      const orderItemDetails = await orderItems.findOne({
        where:{
          id: orderItemsIds[0],
        },
        attributes: [
          'id',
          'orderID',
          [Sequelize.literal(`(select orderServiceType from orders where id = order_items.orderID) `), 'orderServiceType'],
          [Sequelize.literal(`(select pickupCode from orders where id = order_items.orderID) `), 'pickupCode'],
          [Sequelize.literal(`(select barID from orders where id = order_items.orderID) `), 'barID'],
          [Sequelize.literal(`(select userID from orders where id = order_items.orderID) `), 'userID'],
        ],
        include: [
          {
            required: true,
            model: order,
            where: {
              barID: barID
            },
          }
        ]
      });
      pushNotificationForOrdersItems(orderItemDetails, orderStatus)
      res.json({
        success: 1,
        message: 'success!'
      })
    } else{
      res.status(200).json({
        success: 0,
        message: 'No Details found for this order item, please try again.',
      })
    }
  } catch (error) {
    console.log(error)
    res.status(200).send({
      success: 0,
      message: 'error'
    })
  }
}

exports.orderItemPickupUser = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var userID = sessionData.userID
  try {
    let orderItemsIds = req.body.id
    orderItemsIds = orderItemsIds.split(',');
    let isItemStatusUpdated  = false;
    await Promise.all(orderItemsIds.map(async orderItemId => {
      const orderItemDetails = await orderItems.findOne({
        where:{
          id: orderItemId
        },
        attributes: [
          'id',
          'orderID',
          [Sequelize.literal(`(select orderServiceType from orders where id = order_items.orderID) `), 'orderServiceType'],
          [Sequelize.literal(`(select pickupCode from orders where id = order_items.orderID) `), 'pickupCode'],
          [Sequelize.literal(`(select barID from orders where id = order_items.orderID) `), 'barID'],
          [Sequelize.literal(`(select userID from orders where id = order_items.orderID) `), 'userID'],
        ],
        include: [
          {
            required: true,
            model: order,
            where: {
              userID: userID
            },
          }
        ]
      });
      
      if(orderItemDetails){
        isItemStatusUpdated = true;
        
        const updateOrderData = {
          orderStatus: 'Pickedup',
          updatedAt: new Date()
        }
  
        updateOrderData['PickedupTime'] = new Date();
  
        await orderItemDetails.update(updateOrderData)
  
        orderItemWaitTimeNotifications.destroy(
          {
            where: {
              orderItemID: orderItemId
            }
          }
        )

        const TotalOrderItems = await orderItems.count({
          where:{
            orderID: orderItemDetails.orderID,
            isDeleted: 'No',
          },
        });

        const TotalPickedupItems = await orderItems.count({
          where:{
            orderID: orderItemDetails.orderID,
            isDeleted: 'No',
            orderStatus: 'Pickedup'
          },
        });

        if(TotalOrderItems == TotalPickedupItems){
          await order.update({
            orderStatus: 'Pickedup',
            PickedupTime: new Date(),
            updatedAt: new Date()
          },
          {
            where: {
              id: orderItemDetails.orderID,
            }
          })
        }
      }
      
    }))
    if(isItemStatusUpdated){
      res.json({
        success: 1,
        message: 'success!'
      })
    }else{
      res.status(200).json({
        success: 0,
        message: 'No Details found for this order, please try again.',
      })
    }
  } catch (error) {
    console.log(error);
    res.status(200).send({
      success: 0,
      message: 'error'
    })
  }
}

exports.updateOrderItemWaitTime = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  let orderItemsIds = req.body.id.split(',');
  try {
    orderItems.findOne({
      where:{
        id: {
          [Op.in] : orderItemsIds
        },
      },
      include: [
        {
          model: order,
          where : {
            barID: barID
          }
        }
      ],
    }).then(async orderItem => {
      await orderItems.update(
        {
          waitTime: req.body.waitTime
        },
        {
          where: {
            id: {
              [Op.in] : orderItemsIds
            }
          },
        }
      );
      orderItemWaitTimeNotifications.update(
        {
          waitTime: req.body.waitTime
        },
        {
          where: {
            orderItemID: {
              [Op.in] : orderItemsIds
            },
            isDeleted: 'No'
          }
        }
      );
      if (orderItem) {
        res.status(200).send({
          success: 1,
          message: 'Order items wait time updated successfully!',
        })
      } else {
        res.status(200).json({
          success: 0,
          message: 'No Results Found',
          data: {}
        })
      }
    }).error(function (err) {
      res.status(200).json({
        success: 0,
        message: err.message,
      })
    })
  } catch (error) {
    console.log(error);
    res.status(200).send({
      success: 0,
      message: 'error'
    })
  }
}

exports.readyForPickupAlert = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var barID = sessionData.barID
  try {
    order
      .findOne({
        where: {
          id: req.body.id,
          barID: barID
        }
      })
      .then(async orderDataResponse => {
        if (orderDataResponse && Object.keys(orderDataResponse).length > 0) {

          // Delete wait time notification
          orderItemWaitTimeNotifications.destroy(
            {
              where: {
                orderID: req.body.id
              }
            }
          )

          let table_code = await orderTableNumber.findOne({
            attributes: ['tableCode'],
            where: {orderID: req.body.id},
            order: [['id', 'DESC']]
          })
          const tableCode = table_code && table_code.tableCode

          var message = orderDataResponse.dataValues.orderServiceType === 'PICKUP' ? `Your order ${orderDataResponse.dataValues.pickupCode} is ready to collect.` : `Your order for table #${tableCode} is ready.`;

          var notification_type = 'orderReady_pickupAlert'

          if (message != '') {
            userNotification.create({
              barID: barID,
              notification_type: notification_type,
              userID: orderDataResponse.userID,
              dataID: req.body.id,
              message: message,
              createdAt: new Date()
            })
            commonFunction.orderStatusNotificationToUser(orderDataResponse.userID, req.body.id, notification_type, message)
          }
          res.json({
            success: 1,
            message: 'success!'
          })
        } else {
          res.status(200).json({
            success: 0,
            message: 'Order not found!',
          })
        }
      })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.changeTableNumberController = async (req, res) => {
  const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  const userID = sessionData.userID
  try {
    order
      .findOne({
        attributes: [
          'id',
          'orderNo',
          'posOrderId',
          'pickupCode',
          'subTotal',
          'transactionFee',
          'cardType',
          'cardNumber',
          'promocode_id',
          'promocode_amount',
          'tax',
          'total',
          'orderDate',
          'orderStatus',
          'posOrderStatus',
          'PreparingStartTime',
          'ReadyTime',
          'PickedupTime',
          'paymentStatus',
          'orderServiceType',
          [Sequelize.literal(`(select tableCode from orderTableNumber where orderID = orders.id order by id desc limit 1)`), 'tableCode'],
          'userID',
          'barID',
          'transactionID',
          'transferID',
          'refundTransactionID',
          'reversalsTransactionID',
          'refundStatus',
          'isCanceled',
          'intoxicatedDate',
          'createdAt',
          'updatedAt',
          'isDeleted',
        ],
        where: {
          id: req.body.id,
          userID: userID
        }
      })
      .then(async orderDataResponse => {
        if (orderDataResponse) {
          await orderTableNumber
            .findOrCreate({
              defaults: {
                orderID: req.body.id,
                tableCode: req.body.table_code
              },
              where: {
                orderID: req.body.id,
                tableCode: req.body.table_code
              }
            })
          orderDataResponse.dataValues.tableCode = req.body.table_code;
          // await orderDataResponse.reload()
          let message = `Order table number changed`
          barNotification.create({
            barID: orderDataResponse.barID,
            notification_type: 'tableNoChanged',
            userID: userID,
            dataID: req.body.id,
            message: message,
            createdAt: new Date()
          })
          commonFunction.tableChangeNotificationToBarCategoryWise(userID, orderDataResponse.barID, req.body.id, 'tableNoChanged', message, 'Table Number Changed')
          res.json({
            success: 1,
            data: orderDataResponse,
            message: 'Table number updated successfully!'
          })
        } else {
          res.status(200).json({
            success: 0,
            message: 'Order not found!',
          })
        }
      })
  } catch (error) {
    res.status(200)
      .send({
        success: 0,
        message: 'error!'
      })
  }
}

/*
exports.validateVenueId = async (req, res) => {
  try {
    let webhookUrl = `http://${req.headers.host}/order/doshii/webhook`;
    let isValid = await commonFunction.isVenueIdValid('6aAnQN2r');
    if (isValid) {
      await commonFunction.subscribeVenue('6aAnQN2r');
      let result = await commonFunction.setWebhookForVenue(webhookUrl, '6aAnQN2r');
      console.log(result);
    }
    return res.status(200).json({ status: 1, message: 'Locations retrieved successfully!.', data: {} });
  } catch (error) {
    console.log(error);
    return res.status(500).json({ status: 0, message: 'Something went wrong, please try again', data: {} });
  }
}*/

exports.verifyDoshiiOrderWebhook = async (req, res) => {
  console.log('verifyDoshiiOrderWebhook called', req.query, JSON.stringify(req.body));
  var orderData = req.body;
  if (typeof orderData == 'object' && orderData.hasOwnProperty('event')) {
    var orderDetail = await order.findOne({
      where: { posOrderId: orderData.data.id }
    });
    if (orderDetail) {
      await order.update(
        {
          posOrderStatus: orderData.data.status
        },
        {
          where: {
            posOrderId: orderData.data.id
          }
        }
      );

      if (orderData.data.status == 'accepted') {
        var message = `Your Order ID is #${orderDetail.orderNo} accepted from docket machine.`
        await barNotification.create({
          barID: orderDetail.barID,
          notification_type: 'orderAccepted',
          userID: orderDetail.userID,
          dataID: orderDetail.id,
          message: message,
          createdAt: new Date()
        });
        let barNotificationData = await barNotification.findOrCreate({
          where: { barID: orderDetail.barID, notification_type: 'orderAccepted', userID: orderDetail.userID, dataID: orderDetail.id }, defaults: {
            barID: orderDetail.barID,
            notification_type: 'orderAccepted',
            userID: orderDetail.userID,
            dataID: orderDetail.id,
            message: message,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        });
        if (barNotificationData[0].isNewRecord == true) {
          await commonFunction.acceptRejectOrderNotificationToBar(orderDetail.userID, orderDetail.barID, orderDetail.id, { notification_type: 'orderAccepted', title: 'Order Accepted', message });
        }
      }
      if (orderData.data.status == 'rejected') {
        var message = `Your Order ID is #${orderDetail.orderNo} rejected from docket machine.`
        let barNotificationData = await barNotification.findOrCreate({
          where: { barID: orderDetail.barID, notification_type: 'orderRejected', userID: orderDetail.userID, dataID: orderDetail.id }, defaults: {
            barID: orderDetail.barID,
            notification_type: 'orderRejected',
            userID: orderDetail.userID,
            dataID: orderDetail.id,
            message: message,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        });
        if (barNotificationData[0].isNewRecord == true) {
          await commonFunction.acceptRejectOrderNotificationToBar(orderDetail.userID, orderDetail.barID, orderDetail.id, { notification_type: 'orderRejected', title: 'Order Rejected', message });
        }
      }
    }
  }
  return res.status(200).send(req.query.verify);
}

exports.doshiiMenuItemWebhook = async (req, res) => {
  console.log('doshiiMenuItemWebhook called', req.query, JSON.stringify(req.body));
  var webhookData = Object.assign({}, req.body);
  try {
    if (typeof webhookData == 'object' && webhookData.hasOwnProperty('event') && webhookData.data.type == 'products') {
      const barData = await bar.findOne({
        attributes: ['id', 'venueId'],
        where: {
          venueId: webhookData.data.locationId,
          isDeleted: 'No'
        },
        raw: true
      });
      if (barData) {
        var posID = webhookData.data.posId;
        var menuItem = await commonFunction.getDoshiiMenuItemByposID(barData.venueId, posID);

        var categoryData = await categoryModel.findOne({
          where: { name: 'kitchen', isDeleted: 'No' },
          attributes: ['id'],
          raw: true
        });
        for (let sub_category of menuItem.tags) {
          sub_category = commonFunction.titleCase(sub_category);
          // create sub category
          var subCategoryData = await subCategory.findOrCreate({ where: { name: sub_category, categoryID: categoryData.id }, defaults: { name: sub_category, categoryID: categoryData.id, createdAt: new Date(), updatedAt: new Date() } });
        }

        let item = {
          subCategoryID: subCategoryData[0].id,
          name: menuItem.name,
          description: menuItem.description,
          price: (menuItem.unitPrice / 100).toFixed(2),
          posID: menuItem.posId,
          updatedAt: new Date()
        };
        var productData = await product.findOne({ where: { barID: barData.id, posID: posID, isDeleted: 'No' } });
        if (productData) {
          await productData.update(item);

          // create or update product variant
          if (menuItem.options.length > 0) {
            var productVariantsPosIds = [];
            for (let option of menuItem.options) {
              for (let variant of option.variants) {
                if (isNaN(variant.posId)) continue;
                productVariantsPosIds.push(variant.posId);
                let product_variants = {
                  productID: productData.id,
                  variantType: variant.name,
                  price: (variant.price / 100).toFixed(2),
                  posID: variant.posId,
                  productOptionposID: option.posId,
                  productOptionName: option.name,
                  createdAt: new Date(), updatedAt: new Date()
                };
                var productVariantData = await productExtras.findOrCreate({ where: { productID: productData.id, posID: variant.posId, isDeleted: 'No' }, defaults: product_variants });
                if (productVariantData[0].isNewRecord == false) {
                  productVariantData[0].update({ extraItem: variant.name, price: (variant.price / 100).toFixed(2), posID: variant.posId, productOptionposID: option.posId, productOptionName: option.name, updatedAt: new Date() });
                }
              }
            }

            // delte variants from db that are removed from doshii
            if (productVariantsPosIds.length) {
              var checkVariantData = await productExtras.findAll({ attributes: ['id', 'posID'], where: { productID: productData.id, posID: { [Op.notIn]: productVariantsPosIds }, isDeleted: 'No' } });
              for (let iterateExtraItem of checkVariantData) {
                iterateExtraItem.update({ isDeleted: 'Yes' });
              }
            }

          }

          // send notification to bar
          var message = `Menu items has been updated!.`
          await commonFunction.menuUpdateNotificationToBar(barData.id, { notification_type: 'menuUpdated', title: 'Menu Updated', message });

        }
      }
    }
    return res.status(200).send(req.query.verify);
  } catch (error) {
    console.log(error);
    return res.status(200).send(req.query.verify);
  }
}

async function cronToNotifyVenueDoshiiOrders() {
  try {
    var current_date = moment().format('YYYY-MM-DD');
    let substractDateTime = new Date();
    substractDateTime.setMinutes(substractDateTime.getMinutes() - 5);
    let orderData = await order.findAll({
      where: {
        posOrderId: { [Op.ne]: null },
        posOrderStatus: 'Pending',
        paymentStatus: 'received',
        [Op.and]: [
          sequelize.where(sequelize.literal("DATE_FORMAT(orderDate, '%Y-%m-%d')"), '=', current_date),
          sequelize.where(sequelize.literal("DATE_FORMAT(createdAt, '%Y-%m-%d %H:%i')"), '<=', substractDateTime),
        ]
      },
      attributes: ['id', 'userID', 'barID', [Sequelize.literal(`IF(orderServiceType='TABLE', tableCode, pickupCode)`), 'pickupCode'], 'posOrderId', 'posOrderStatus']
    });
    for (let orderDetail of orderData) {
      var message = `The order #${orderDetail.pickupCode} from pos has not been accepted yet.`
      let barNotificationData = await barNotification.findOrCreate({
        where: { barID: orderDetail.barID, notification_type: 'orderPending', userID: orderDetail.userID, dataID: orderDetail.id }, defaults: {
          barID: orderDetail.barID,
          notification_type: 'orderPending',
          userID: orderDetail.userID,
          dataID: orderDetail.id,
          message: message,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
      if (barNotificationData[0]._options.isNewRecord) {
        var barData = await bar.findOne({
          where: { id: orderDetail.barID },
          attributes: ['venueId'],
          raw: true
        });
        if (!barData.venueId) {
          break;
        }
        let doshiOrderDetails = await commonFunction.getOrderDetailsFromDoshii(barData.venueId, orderDetail.posOrderId);
        if (doshiOrderDetails.version) {
          let doshiiUpdateOrder = await commonFunction.updateOrderinDoshii(barData.venueId, orderDetail.posOrderId, {
            "status": "cancelled",
            // "mealPhase": "ordered",
            "version": doshiOrderDetails.version
          });
          if (doshiOrderDetails.transactions && Array.isArray(doshiOrderDetails.transactions) && doshiOrderDetails.transactions.length) {
            if (doshiOrderDetails.transactions[0].status == 'pending') {
              let doshiiUpdateOrderTransaction = await commonFunction.updateOrderTransactioninDoshii(barData.venueId, doshiOrderDetails.transactions[0].id, {
                "status": "cancelled",
                "version": doshiOrderDetails.transactions[0].version
              });
            }
          }
          if (typeof doshiiUpdateOrder == 'object' && doshiiUpdateOrder.hasOwnProperty('id') && orderDetail.posOrderStatus == 'Pending') {
            await orderDetail.update({ posOrderStatus: 'Cancelled' });
          }
        }

        await commonFunction.acceptRejectOrderNotificationToBar(orderDetail.userID, orderDetail.barID, orderDetail.id, { notification_type: 'orderPending', title: 'Order Pending', message });
        console.log(JSON.stringify(barNotificationData[0]));
      }
    }
  } catch (err) {
    console.log('cronToNotifyVenueDoshiiOrders cron err ', err);
  }
}

exports.orderPosHandling = class orderPosHandling {
  constructor(barID) {
    this.barID = barID
    this.posConfigured = null;
    this.KOUNTA_ORDER_STATUS = {
      SUBMITTED: 'SUBMITTED',
      COMPLETE: 'COMPLETE',
      ON_HOLD: 'ON_HOLD',
      PENDING: 'PENDING',
      ACCEPTED: 'ACCEPTED',
      REJECTED: 'REJECTED'
    }
    this.KOUNTA_ORDER_CONVERSION = {
      SUBMITTED: 'New',
      ACCEPTED: 'Preparing',
      COMPLETE: 'Pickedup',
      REJECTED: 'NotPickedup'
    }
  }

  upsertKountaOrderItems = async (orders) => {
    const isSingleRecord = !Array.isArray(orders)
    let ordersIterable = isSingleRecord ? [orders] : orders;
    for (let orderIter of ordersIterable) {
      let {
        id: posID, sale_number, status, total, notes: orderNumber, deleted, updated_at: orderUpdatedAt, fulfil_at
      } = orderIter

      // get order details
      const orderDatabaseDetail = await order.findOne({ where: { posOrderId: posID } })
      if (!orderDatabaseDetail)
        continue

      // get order kounta details
      const orderDetail = await this.posConfigured.fetchApi(this.posConfigured.URL_CONVERSION.KOUNTA_VIEW_ORDER_URL(posID));
      let { unit_price, customer } = orderDetail
      // const {id: customerPosId, email: customerEmail, primary_email_address: customerPrimaryEmail} = customer

      let orderResponse = {
        total: total,
        posAssignedNumber: sale_number,
        posOrderStatus: status,
        PickedupTime: fulfil_at,
        updatedAt: orderUpdatedAt,
        isDeleted: deleted ? 'Yes' : 'No'
      };

      try {
        // create or update order
        orderResponse['orderStatus'] = this.KOUNTA_ORDER_CONVERSION[status]
        if (orderResponse['orderStatus'] === 'NotPickedup')
          orderResponse['isCanceled'] = 'Yes'

        order.update(orderResponse, {
          where: {
            barID: this.barID,
            [Op.or]: [{ posOrderId: posID }, { orderNo: orderNumber }],
            isDeleted: 'No'
          }
        })

      } catch (e) {
        console.log("From order sync: ", e)
      }
    }
  }
}

// For Restricting an order when active time of products in cart gets finished before order is being placed...
exports.cartGetItems = async (req, res, next) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var userID = sessionData.userID
  var productIDs = JSON.parse(req.body.productIDs)
  var subCatIds = []
  var proSubCatIds = []

  try {

    let proIds = await product.findAll({
      attributes: [
        'name',
        'subCategoryID'
      ],
      where: {
        id: { [Op.in]: productIDs }
      }
    }).then(async result => {
      for (const ele of result) {
        proSubCatIds.push(ele.subCategoryID)
      }
    })
    let itemActiveSubids = await itemActiveHours.findAll({
      attributes: [
        'subCategoryID',
      ],
      where: {
        subCategoryID: [Sequelize.literal(`SELECT subCategoryID FROM product WHERE id IN (${productIDs}) AND product.isDeleted = "No" AND product.status = "Active"`)],
        status: '1',
        barID: req.body.barID,
        weekDay: Sequelize.fn('WEEKDAY', Sequelize.cast(moment().tz('Australia/Perth').format('YYYY-MM-DD'), 'date'))
      },
      raw: true
    }).then(async results => {
      for (const ele of results) {
        subCatIds.push(ele.subCategoryID)
      }

      let difference = proSubCatIds.filter(x => !subCatIds.includes(x)).concat(subCatIds.filter(y => !proSubCatIds.includes(y)));
      if (difference.length > 0) {
        cartItems.destroy(
          {
            where: {
              subCategoryID: { [Op.in]: difference },
              productID: { [Op.in]: productIDs },
              barID: req.body.barID,
            }
          }
        )
          .then(async function () {
            res.status(200).send({
              success: -3,
              data: [],
              message: 'Some of the items in your cart have been removed as they are currently unavailable.'
            })
          })
          .error(function (err) {
            res.status(200).json({
              success: 0,
              message: err.message,
            })
          })
      } else {
        let isNext = true;
        const barData = await bar.findOne({
          attributes: [
            'id', 'restaurantName', 'managerName', 'email', 'countryCode', 'mobile',
            'stripeID', 'venueId', 'attachedPosConfig', 'docketCommission', 'docketStatus', 'serviceType'
          ],
          where: {
            id: req.body.barID
          },
        });
        // Check bar service type
        if(barData.dataValues.serviceType != "BOTH" && barData.dataValues.serviceType != req.body.order_service_type) {
          isNext = false;
          await cartItems.destroy({
            where:{
              barID: req.body.barID,
            },
          });
          return res.status(200).send({
            success: -4,
            data: {},
            message: 'Sorry, Some of the cart items services are not currently provided by this venue. We are discarding those items from your cart to proceed further.'
          }); 
        }

        // Check product service type
        let checkRequestOrderItems = JSON.parse(req.body.orderItems);
        let removeCartData = []; 
        for (let requestedOrderItem of checkRequestOrderItems) {
          let productData = await commonFunction.getProductDetail(requestedOrderItem['productID']);
          if(productData.dataValues.serviceType != "BOTH" && productData.dataValues.serviceType != requestedOrderItem['cartServiceType']) {
            removeCartData.push(requestedOrderItem['id']);
          }

          const cartProductVariantTypes = requestedOrderItem['productVariantTypes'];
          if (Array.isArray(cartProductVariantTypes) && cartProductVariantTypes.length > 0) {
            for (const productVariantType of cartProductVariantTypes) {
              const productVariantTypeData = await productVariantTypes.findOne({ where: { id: productVariantType['productVariantTypeID'] } });
                  if(productVariantTypeData.dataValues.serviceType != "BOTH" && productVariantTypeData.dataValues.serviceType != requestedOrderItem['cartServiceType']) {
                    removeCartData.push(requestedOrderItem['id']);
                  }
            }
          }
        }
    
        if(removeCartData.length > 0) {
          isNext = false;
          await cartItems.destroy({
            where:{
              id: {
                [Op.in] : removeCartData
              },
            },
          });
          await cartProductVariantTypes.destroy({ where: { cartItemID: {
            [Op.in] : removeCartData
          } } });
          await cartProductVariantSubTypes.destroy({ where: { cartItemID: {
            [Op.in] : removeCartData
          } } });
          
          return res.status(200).send({
            success: -4,
            data: {},
            message: 'Sorry, Some of the cart items services are not currently provided by this venue. We are discarding those items from your cart to proceed further.'
          }); 
        }
    
        if(isNext) {
          next();
        }
      }
    })
  } catch (e) {
    console.log(e)
  }
}

exports.cartGetItemsV2 = async (req, res, next) => {
  const currentDateTimeUTC = req.body.currentDateTimeUTC || moment().utc().format('YYYY-MM-DD HH:mm:ss');
  const [currentDay, currentTime] = [moment(currentDateTimeUTC).isoWeekday() - 1, moment(currentDateTimeUTC).format('HH:mm:ss')];
  const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  const userID = sessionData.userID;
  const productIDs = JSON.parse(req.body.productIDs);
  const subCatIds = [], proSubCatIds = [];

  try {
    const products = await product.findAll({ attributes: ['name', 'subCategoryID'], where: { id: { [Op.in]: productIDs } } });
    products.forEach(({ subCategoryID }) => proSubCatIds.push(subCategoryID));

    const results = await barSubCategoryOpeningHoursUTC.findAll({
      attributes: ['subCategoryID'],
      where: {
        subCategoryID: [Sequelize.literal(`SELECT subCategoryID FROM product WHERE id IN (${productIDs}) AND product.isDeleted = "No" AND product.status = "Active"`)],
        isClosed: '0',
        barID: req.body.barID,
        weekDay: currentDay,
        openingHours: { [Op.lte]: currentTime },
        closingHours: { [Op.gte]: currentTime }
      },
      raw: true
    });

    results.forEach(({ subCategoryID }) => subCatIds.push(subCategoryID));
    let difference = proSubCatIds.filter(x => !subCatIds.includes(x)).concat(subCatIds.filter(y => !proSubCatIds.includes(y)));

    if (difference.length > 0) {
      await cartItems.destroy({ where: { subCategoryID: { [Op.in]: difference }, productID: { [Op.in]: productIDs }, barID: req.body.barID } });
      return res.status(200).send({ success: -3, data: {}, message: 'Some of the items in your cart have been removed as they are currently unavailable.' });
    }

    const barData = await bar.findOne({
      attributes: ['id', 'restaurantName', 'managerName', 'email', 'countryCode', 'mobile', 'stripeID', 'venueId', 'attachedPosConfig', 'docketCommission', 'docketStatus', 'serviceType'],
      where: { id: req.body.barID }
    });

    if (barData.dataValues.serviceType !== "BOTH" && barData.dataValues.serviceType !== req.body.order_service_type) {
      await cartItems.destroy({ where: { barID: req.body.barID } });
      return res.status(200).send({ success: -4, data: {}, message: 'Sorry, Some of the cart items services are not currently provided by this venue. We are discarding those items from your cart to proceed further.' });
    }

    const checkRequestOrderItems = JSON.parse(req.body.orderItems);
    let removeCartData = [];

    for (const { productID, cartServiceType, id, reqProductVariantTypes } of checkRequestOrderItems) {
      const productData = await commonFunction.getProductDetail(productID);
      if (productData.dataValues.serviceType !== "BOTH" && productData.dataValues.serviceType !== cartServiceType) removeCartData.push(id);
      if (Array.isArray(reqProductVariantTypes) && reqProductVariantTypes.length) {
        for (const { productVariantTypeID } of reqProductVariantTypes) {
          const productVariantTypeData = await productVariantTypes.findOne({ where: { id: productVariantTypeID } });
          if (productVariantTypeData.dataValues.serviceType !== "BOTH" && productVariantTypeData.dataValues.serviceType !== cartServiceType) removeCartData.push(id);
        }
      }
    }

    if (removeCartData.length > 0) {
      await cartItems.destroy({ where: { id: { [Op.in]: removeCartData } } });
      await cartProductVariantTypes.destroy({ where: { cartItemID: { [Op.in]: removeCartData } } });
      await cartProductVariantSubTypes.destroy({ where: { cartItemID: { [Op.in]: removeCartData } } });
      return res.status(200).send({ success: -4, data: {}, message: 'Sorry, Some of the cart items services are not currently provided by this venue. We are discarding those items from your cart to proceed further.' });
    }

    next();
  } catch (e) {
    console.log(e);
  }
};

exports.printDocketOrderResponse = async (req, res) => {
  try {
    let orderID = req.body.orderID
    // let tableCategory = [1]
    const orderAttributes = [
      'id',
      'orderNo',
      'subTotal',
      'transactionFee',
      'tax',
      'total',
      'orderDate',
      'orderStatus',
      'pickupCode',
      [Sequelize.literal(`(select tableCode from orderTableNumber where orderID = orders.id order by id desc limit 1)`), 'tableCode'],
      'promocode_id',
      'promocode_amount',
      'userID',
      'barID',
      'orderServiceType',
      'docketPrintingStatus',
      'createdAt'
    ]
    const itemAttributes = [
      'id',
      'orderID',
      'productID',
      'price',
      'quantity',
      'specialRequest',
      'isCanceled',
      'refundAmount',
      'refundedQuantity'
    ]
    const productAttributes = [
      'id',
      'name',
      'categoryID',
      'description',
      'avatar',
      'subCategoryID'
    ]
    order.findOne({
      where: { id: orderID, isDeleted: 'No' },
      attributes: orderAttributes,
      include: [
        {
          required: true,
          attributes: itemAttributes,
          model: orderItems,
          include: [
            {
              // where: { categoryID: tableCategory },
              attributes: productAttributes,
              model: product,
              include: [
                {
                  attributes: [
                    'id',
                    'description',
                    'address'
                  ],
                  model: pickupLocation,
                }
              ]
            },
            {
              attributes: [
                'id',
                'orderItemID',
                'productExtrasID',
                'price'
              ],
              model: orderItemExtras,
              include: [
                {
                  attributes: [
                    'id',
                    'extraItem'
                  ],
                  model: productExtras,
                }
              ]
            },
            {
              attributes: [
                'id',
                'orderItemID',
                'productVariantsID',
                'price'
              ],
              model: orderItemVariants,
              include: [
                {
                  attributes: [
                    'id',
                    'variantType'
                  ],
                  model: productVariants,
                }
              ]
            },
            {
              attributes: [
                ['id', "orderProductVariantTypeID"],
                'orderItemID',
              ],
              where: Sequelize.where(Sequelize.col('`order_items`.`id`'), Sequelize.col('`order_items->order_product_variant_types`.`orderItemID`')),
              model: orderProductVariantTypes,
              required: false,
              include: [
                {
                  attributes: [
                    ['id', "productVariantTypeID"],
                    'label',
                  ],
                  model: productVariantTypes,
                  required: true,
                  include: [
                    {
                      attributes: [
                        ['id', "orderProductVariantSubTypeID"],
                        'orderItemID',
                      ],
                      where: Sequelize.where(Sequelize.col('`order_items->order_product_variant_types->product_variant_type->order_product_variant_sub_type`.`orderProductVariantTypeID`'), Sequelize.col('`order_items->order_product_variant_types`.`id`')),
                      model: orderProductVariantSubTypes,
                      include: [
                        {
                          attributes: [
                            ['id', "productVariantSubTypeID"],
                            ['variantType', "extraItem"],
                            'price',
                          ],
                          model: productVariantSubTypes,
                        }
                      ]
                    }
                  ]
                }
              ],
            }
          ]
        },
        {
          required: true,
          model: user,
          attributes: ['id', 'fullName', 'mobile', 'email']
        },
        {
          model: coupons,
          attributes: ['id', 'code', 'name', 'description']
        },
        {
          model: orderTableNumber,
          attributes: ['tableCode']
        },
        {
          model: bar,
          attributes: ['docketSubscribed']
        }
      ],
      order: [['createdAt', 'ASC']],
      distinct: true,
      duplicating: false
    })
      .then(result => {
        res.status(200).send({
          success: 1,
          data: result,
          message: 'Order Response given to Docket successfully.'
        })
      })
  } catch (error) {
    console.log(error)
  }
}

exports.sendInvoice = async (req, res, next) => {
  try {
    var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);        
    var userDetails = await user.findOne({
      attributes: ['id', 'email', 'fullName'],
      where: {
        id: sessionData.userID,
        isDeleted: 'No'
      }
    });   

    await uploadImage.any('receipt')(req, res, async function (err) {
      let username = '';
      if (typeof req.body.name !== 'undefined' && req.body.name !== null) {  
        username = req.body.name;
      } else {
        username = userDetails.fullName;
      }

      if (err) {
        console.error("❌ Upload Error:", err);
      }
      // Multer file validation
      if (err && err.code == 'LIMIT_FILE_SIZE') {
        return res.status(500).send({
          success: 0,
          message: 'File Size is too large. Allowed file size is 5MB'
        });
      } else if (err && err.code == 'LIMIT_FILE_TYPE') {
        return res.status(500).send({
          success: 0,
          message: err.message
        });
      }
      if (req.body.isError) {
        res.status(200).send({
          success: 0,
          message: req.body.message
        })
      } else {
        var receipt = '';
        if (req.files && req.files.length > 0) {
          for (var i = 0; i < req.files.length; i++) {
            if (req.files[i].fieldname == 'receipt') {
              receipt = req.files[i].key
            }
          }
        }
        if(!receipt) {
          res.status(200).send({
            success: 0,
            message: "File not uploaded"
          })
          return false;
        }
        let receiptImg = env.awsPublicServerURL + 'order-receipt/' + receipt;
        
        const today = new Date();
		    const year = today.getFullYear();
        const subject = "Your MyTab Order #"+req.body.orderId+" Receipt"
        let mailOptions = {
          from: `MyTab <${env.customerEmailTo}>`,
          to: req.body.recipientEmail, // '<EMAIL>'
          subject: subject,
          html: `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8"><base href="/"/><title>${subject}</title>
            </head>
            <body style="padding:0; margin:0; color:#6e6e6e; font-family:Verdana; -webkit-text-size-adjust: none;">
              <table style="-webkit-text-size-adjust: none;" width="100%" cellspacing="0" cellpadding="0" border="0">
                <tr>
                  <td align="center">
                    <table width="100%" cellpadding="0" cellspacing="0" border="0" style="min-width:200px; border: 1px solid #6e6e6e; max-width:600px; ">
                      <tr style="border-bottom: 3px solid #58585a">
                        <td>
                          <center>
                            <a style="color:#6e6e6e; text-decoration:none;" href="#">
                              <h1 style="margin-top:30px">
                                <img src=${process.env.logo} alt=MyTab style="border-width:0; max-width:164px;height:55px; display:block; " />
                              </h1>
                            </a>
                          </center>
                        </td>
                      </tr>
                      <tr>
                        <td style="padding: 15px; font-size: 10pt; line-height: 22px;">
                          <div>
                            <p>Hello ${username}, </p>
                            <p>Thank you for supporting your favourite local venues on MyTab. As requested, you will find the receipt for your order attached below.</p>
                            <p>We truly appreciate your support and are dedicated to connecting you to all your favourite venues.</p>
                            <p>Should you need to get in touch with us, feel free to email <a href="mailto:<EMAIL>" style="color:#ff6460"><EMAIL></a> and our team will be happy to assist you.</p>
                            <p>Once again, thank you for being a part of our foodie community!</p>
                            <p>MyTab Customer Support <br /> <a href="https://www.mytabinfo.com" style="color:#ff6460">www.mytabinfo.com</a></p>
                          </div>
                        </td>
                      </tr>
                      <tr>
                        <td style="text-align: center; background-color:#58585a; color: #fff; padding-top: 5px; padding-bottom: 5px; font-weight: bold; font-size: 30px;" colspan="2"></td>
                      </tr>
                      <tr>
                        <td style="text-align: center; background-color: #58585a; color: #fff; padding: 0px 0px 10px;" colspan="2">
                          <p style="font-size: 12px;line-height: 1.6;font-weight: normal;"> &copy; ${year} MyTab | All&nbsp;Rights&nbsp;Reserved</p>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
              </table>
            </body>
          </html>`,
          attachments: [
            {
              filename: receipt,
              path: receiptImg,
            }
          ]
        }
        await common.sendEmail(mailOptions);
        res.status(200).send({
          success: 1,
          data: {},
          message: "Email sent successfully."
        })
      }
    });       
  } catch (error) {
    console.log("error", error);
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.barOrderHistoryNew = async (req, res) => {
  try {
    const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    const barID = sessionData.barID;
    const whereClauseOrder = [];
    const whereClauseRefundedItem = []

    const orderAttributes = [
      'id',
      'orderNo',
      'subTotal',
      'transactionFee',
      'tax',
      'total',
      'orderDate',
      'orderStatus',
      'refundStatus',
      'promocode_id',
      'promocode_amount',
      'promocode_discount',
      'userID',
      'barID',
      'orderServiceType',
      [
        Sequelize.literal(
          '(select count(order_items.id) from order_items WHERE order_items.orderID = `orders`.id AND order_items.isCanceled="No")'
        ),
        'totalCancelItem'
      ],
      [
        Sequelize.literal(
          '(select sum(order_tax.amount) from order_tax WHERE order_tax.orderID = `orders`.id)'
        ),
        'totalOrderTax'
      ],
      [
        Sequelize.literal(
          '(select sum(order_refund_tax.amount) from order_refund_tax WHERE order_refund_tax.orderID = `orders`.id)'
        ),
        'totalOrderRefundTax'
      ],
      'createdAt'
    ];

    whereClauseOrder.push({
      isDeleted: 'No',
      barID: barID
    })
    
    if(req.body.startDate) {
      whereClauseOrder.push({
        orderDate : {
          [Op.gte]: req.body.startDate
        }
      });
    }
    
    if(req.body.endDate) {
      whereClauseOrder.push({
        orderDate : {
          [Op.lte]: req.body.endDate
        }
      });
    }
    
    if(req.body.search) {
      whereClauseOrder.push({
        orderNo : {
          [Op.like]: '%' + req.body.search + '%'
        }
      });
    }
    
    if (req.body.orderType && req.body.orderType != '') {
      if (req.body.orderType == 'current') {
        whereClauseOrder.push({
          [Op.or]: [
            sequelize.where(sequelize.col('orders.orderStatus'), '=', 'New'),
            sequelize.where(sequelize.col('orders.orderStatus'), '=', 'Preparing'),
            // {
            //   orderStatus: 'Pickup'
            // }
          ],
          isCanceled: 'No',
          paymentStatus: 'received'
        })
      } else if (req.body.orderType == 'past') {
        whereClauseOrder.push(Sequelize.where(Sequelize.col('orders.orderStatus'), 'not in', [['New', 'Preparing']]))
        whereClauseOrder.push({
          //isCanceled: 'No',
          paymentStatus: 'received'
        })
      } else if (req.body.orderType == 'refund') {
        whereClauseOrder.push({
          [Op.or]: [
            {
              refundStatus: 'Refunded'
            },
            {
              refundStatus: 'PartialRefunded'
            },
            sequelize.where(sequelize.col('orders.orderStatus'), '=', 'Intoxicated'),
          ],
          paymentStatus: 'received'
        })
        // whereClauseRefundedItem.push({
        //   [Op.or]: [
        //     {
        //       isCanceled: 'Yes'
        //     },
        //     {
        //       refundedQuantity: {
        //         [Op.gt]: 0
        //       }
        //     },
        //     sequelize.where(sequelize.col('orders.orderStatus'), '=', 'Intoxicated')
        //   ]
        // })
      } else if (req.body.orderType == 'cancel') {
        whereClauseOrder.push({
          [Op.and]: [
            {
              isCanceled: 'Yes',
              refundStatus: 'No'
            }
          ]
        })
      } else if (req.body.orderType == 'Intoxicated') {
        whereClauseOrder.push({
          [Op.and]: [
            sequelize.where(sequelize.col('orders.orderStatus'), '=', 'Intoxicated'),
            {
              isCanceled: 'No',
              refundStatus: 'No'
            }
          ]
        })
      } else if (req.body.orderType == 'promo_code') {
        whereClauseOrder.push({
          [Op.and]: [
            {
              promocode_id: {
                [Op.gt]: 0
              },
              promocode_amount: {
                [Op.gt]: 0
              },
            }
          ]
        })
      }
    } else {
      whereClauseOrder.push({
        [Op.or]: [
          sequelize.where(sequelize.col('orders.orderStatus'), '=', 'New'),
          sequelize.where(sequelize.col('orders.orderStatus'), '=', 'Preparing'),
          sequelize.where(sequelize.col('orders.orderStatus'), '=', 'Pickup'),
        ],
        paymentStatus: 'received'
      })
    }

    let page = req.body.page ? req.body.page : 1;
    const offset = (page - 1) * 10;
    const limit = 10;
    
    const orderHistoryList = await order
      .findAndCountAll({
        where: [
          ...whereClauseOrder,
        ],
        attributes: [...orderAttributes, 'pickupCode'],
        include: [
          {
            required: true,
            attributes: [
              'id',
              'orderID',
              'productID',
              'price',
              'quantity',
              'specialRequest',
              'isCanceled',
              'refundAmount',
              'refundedQuantity'
            ],
            model: orderItems,
            where: whereClauseRefundedItem,
            include: [
              {
                required: false,
                where: { categoryID: [1,2] },
                attributes: [
                  'id',
                  'name',
                  'categoryID',
                  'subCategoryID',
                  'description',
                  'avatar',
                  'posID',
                ],
                model: product,
                include: [
                  {
                    attributes: [
                      'id',
                      'description',
                      'address'
                    ],
                    model: pickupLocation,
                  }
                ]
              },
              {
                attributes: [
                  'id',
                  'orderItemID',
                  'productExtrasID',
                  'price'
                ],
                model: orderItemExtras,
                include: [
                  {
                    attributes: [
                      'id',
                      'extraItem',
                      'posID',
                    ],
                    model: productExtras,
                  }
                ]
              },
              {
                attributes: [
                  'id',
                  'orderItemID',
                  'productVariantsID',
                  'price'
                ],
                model: orderItemVariants,
                include: [
                  {
                    attributes: [
                      'id',
                      'variantType'
                    ],
                    model: productVariants,
                  }
                ]
              },
              {
                attributes: [
                  ['id', "orderProductVariantTypeID"],
                  'orderItemID',
                ],
                where: Sequelize.where(Sequelize.col('`order_items`.`id`'), Sequelize.col('`order_items->order_product_variant_types`.`orderItemID`')),
                model: orderProductVariantTypes,
                required: false,
                include: [
                  {
                    attributes: [
                      ['id', "productVariantTypeID"],
                      'label',
                    ],
                    model: productVariantTypes,
                    required: true,
                    include: [
                      {
                        attributes: [
                          ['id', "orderProductVariantSubTypeID"],
                          'orderItemID',
                        ],
                        // where: Sequelize.where(Sequelize.col('`order_items->order_product_variant_types->product_variant_type->order_product_variant_sub_type`.`orderProductVariantTypeID`'), Sequelize.col('`order_items->order_product_variant_types`.`id`')),
                        model: orderProductVariantSubTypes,
                        include: [
                          {
                            attributes: [
                              ['id', "productVariantSubTypeID"],
                              ['variantType', "extraItem"],
                              'price',
                            ],
                            model: productVariantSubTypes,
                          }
                        ]
                      }
                    ]
                  }
                ],
              }
            ]
          },
          {
            required: true,
            model: user,
            attributes: ['id', 'fullName', 'mobile', 'email']
          },
          {
            model: coupons,
            attributes: ['id', 'code', 'name', 'description']
          },
          {
            model: orderTableNumber,
            attributes: ['tableCode']
          },
          {
            attributes: [
              'id','name', 'percentage', "taxID", "amount"
            ],
            model: orderTax,
          },
          {
            attributes: [
              'id','name', 'percentage', "taxID", "amount"
            ],
            model: orderRefundTax,
          },
        ],
        order: [['createdAt', 'DESC']],
        distinct: true,
        duplicating: false,
        offset: offset,
        limit: limit,
      });
    if(orderHistoryList.count > 0){
      for (let i = 0; i < orderHistoryList.rows.length; i++) {
        const order = orderHistoryList.rows[i];
        for (let j = 0; j < order.order_items.length; j++) {
          const item = orderHistoryList.rows[i].order_items[j];
          for (let k = 0; k < item.order_product_variant_types.length; k++) {
            const variant = orderHistoryList.rows[i].order_items[j].order_product_variant_types[k];
            let getProductSubVariant = await orderProductVariantSubTypes.find({
                attributes: [
                  ['id', "orderProductVariantSubTypeID"],
                  "orderItemID",
                  [
                    Sequelize.literal(
                      '(select variantType from product_variant_sub_types WHERE order_product_variant_sub_types.productVariantSubTypeID = `product_variant_sub_types`.id)'
                    ),
                    'extraItem'
                  ],
                  [
                    Sequelize.literal(
                      '(select price from product_variant_sub_types WHERE order_product_variant_sub_types.productVariantSubTypeID = `product_variant_sub_types`.id)'
                    ),
                    'price'
                  ],
                  [
                    Sequelize.literal(
                      '(select id from product_variant_sub_types WHERE order_product_variant_sub_types.productVariantSubTypeID = `product_variant_sub_types`.id)'
                    ),
                    'productVariantSubTypeID'
                  ],
                ],
                where: {
                  orderItemID: variant.orderItemID,
                  productVariantTypeID: variant.product_variant_type.dataValues.productVariantTypeID,
                }
            });

            orderHistoryList.rows[i].order_items[j].order_product_variant_types[k].dataValues.product_variant_type.dataValues.order_product_variant_sub_type.product_variant_sub_type.dataValues.productVariantSubTypeID = getProductSubVariant.dataValues.productVariantSubTypeID;
            
            orderHistoryList.rows[i].order_items[j].order_product_variant_types[k].dataValues.product_variant_type.dataValues.order_product_variant_sub_type.product_variant_sub_type.dataValues.extraItem = getProductSubVariant.dataValues.extraItem;
            
            orderHistoryList.rows[i].order_items[j].order_product_variant_types[k].dataValues.product_variant_type.dataValues.order_product_variant_sub_type.product_variant_sub_type.dataValues.price = getProductSubVariant.dataValues.price;

          }
        }
      }
    }

    res.status(200).send({
      success: 1,
      message: 'order list retrieve successfully!',
      data: orderHistoryList
    })  
  } catch (error) {
    console.log(error)
    res.status(200).send({
      success: 0,
      message: error
    })
  }
}


exports.barOrderHistoryNewUpdated = async (req, res) => {
  try {
    const sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
    const barID = sessionData.barID;
    // let getUserCategory = await barAccessToken.findOne({
    //   where: {
    //     barID: barID,
    //     accessToken: req.headers.accesstoken,
    //   }
    // })
    // getUserCategory = getUserCategory?.subCategoryIDs  !== null ? getUserCategory?.subCategoryIDs?.split(',') : [];
    
    const whereClauseOrder = [];
    const whereClauseProduct = [];
    const whereClauseOrderItems = [];
    const whereClauseRefundedItem = []

    const orderAttributes = [
      'id',
      'orderNo',
      'subTotal',
      'transactionFee',
      'refundTransactionFee',
      'tax',
      'total',
      'orderDate',
      'orderStatus',
      'refundStatus',
      'paymentType',
      'promocode_id',
      'promocode_amount',
      'promocode_discount',
      'userID',
      'barID',
      'orderServiceType',
      [
        Sequelize.literal(
          '(select count(order_items.id) from order_items WHERE order_items.orderID = `orders`.id AND order_items.isCanceled="No")'
        ),
        'totalCancelItem'
      ],
      [
        Sequelize.literal(
          '(select count(order_items.id) from order_items WHERE order_items.orderID = `orders`.id)'
        ),
        'totalOrderItems'
      ],
      [
        Sequelize.literal(
          '(select sum(order_tax.amount) from order_tax WHERE order_tax.orderID = `orders`.id)'
        ),
        'totalOrderTax'
      ],
      [
        Sequelize.literal(
          '(select sum(order_refund_tax.amount) from order_refund_tax WHERE order_refund_tax.orderID = `orders`.id)'
        ),
        'totalOrderRefundTax'
      ],
      'createdAt'
    ];

    whereClauseOrder.push({
      isDeleted: 'No',
      barID: barID
    })
    
    // if(getUserCategory.length > 0) {
    //   whereClauseProduct.push({
    //     subCategoryID : getUserCategory
    //   });
    // }

    if(req.body.startDate) {
      whereClauseOrder.push({
        convertedOrderDate : {
          [Op.gte]: req.body.startDate
        }
      });
    }
    
    if(req.body.endDate) {
      whereClauseOrder.push({
        convertedOrderDate : {
          [Op.lte]: req.body.endDate
        }
      });
    }
    
    if(req.body.search) {
      whereClauseOrder.push({
        orderNo : {
          [Op.like]: '%' + req.body.search + '%'
        }
      });
    }
    
    if (req.body.orderType && req.body.orderType != '') {
      if (req.body.orderType == 'current') {
        whereClauseOrder.push({
          [Op.or]: [
            {
              orderStatus: 'New'
            },
            {
              orderStatus: 'Preparing'
            },
          ],
          isCanceled: 'No',
          paymentStatus: 'received'
        })
        whereClauseOrderItems.push({
          [Op.or]: [
            {
              orderStatus: 'New'
            },
            {
              orderStatus: 'Preparing'
            },
            //   orderStatus: 'Pickup'
            // }
          ],
          isCanceled: 'No',
        })
      } else if (req.body.orderType == 'past') {
        whereClauseOrderItems.push({
          orderStatus: {
            [Op.notIn]: ['New', 'Preparing']
          }
        })
        whereClauseOrder.push({
          //isCanceled: 'No',
          paymentStatus: 'received'
        })
      } else if (req.body.orderType == 'refund') {
        whereClauseOrder.push({
          [Op.or]: [
            {
              refundStatus: 'Refunded'
            },
            {
              refundStatus: 'PartialRefunded'
            },
            sequelize.where(sequelize.col('orders.orderStatus'), '=', 'Intoxicated'),
          ],
          paymentStatus: 'received'
        })
        // whereClauseRefundedItem.push({
        //   [Op.or]: [
        //     {
        //       isCanceled: 'Yes'
        //     },
        //     {
        //       refundedQuantity: {
        //         [Op.gt]: 0
        //       }
        //     },
        //     sequelize.where(sequelize.col('orders.orderStatus'), '=', 'Intoxicated')
        //   ]
        // })
      } else if (req.body.orderType == 'cancel') {
        whereClauseOrderItems.push({
          [Op.and]: [
            {
              isCanceled: 'Yes',
            }
          ]
        })
        whereClauseOrder.push({
          [Op.and]: [
            {
              refundStatus: 'No'
            }
          ]
        })
      } else if (req.body.orderType == 'Intoxicated') {
        whereClauseOrderItems.push({
          [Op.and]: [
            {
              isCanceled: 'No',
            }
          ]
        })
        whereClauseOrder.push({
          [Op.and]: [
            sequelize.where(sequelize.col('orders.orderStatus'), '=', 'Intoxicated'),
            {
              refundStatus: 'No'
            }
          ]
        })
      } else if (req.body.orderType == 'promo_code') {
        whereClauseOrder.push({
          [Op.and]: [
            {
              promocode_id: {
                [Op.gt]: 0
              },
              promocode_amount: {
                [Op.gt]: 0
              },
            }
          ]
        })
      }
    } else {
      whereClauseOrder.push({
        // [Op.or]: [
        //   {
        //     orderStatus: 'New'
        //   },
        //   {
        //     orderStatus: 'Preparing'
        //   },
        //   {
        //     orderStatus: 'Pickup'
        //   }
        // ],
        paymentStatus: 'received'
      })
      whereClauseOrderItems.push({
        [Op.or]: [
          {
            orderStatus: 'New'
          },
          {
            orderStatus: 'Preparing'
          },
          {
            orderStatus: 'Pickup'
          }
        ],
      })
    }

    let page = req.body.page ? req.body.page : 1;
    const offset = (page - 1) * 10;
    const limit = 10;
    
    const orderHistoryList = await order
      .findAndCountAll({
        where: [
          ...whereClauseOrder,
        ],
        attributes: [...orderAttributes, 'pickupCode'],
        include: [
          {
            required: true,
            attributes: [
              'id',
              'orderID',
              'productID',
              'price',
              'quantity',
              'specialRequest',
              'isCanceled',
              'refundAmount',
              'refundedQuantity',
              'waitTime',
              'orderStatus',
              'PreparingStartTime',
              'ReadyTime',
              'PickedupTime',
              [Sequelize.literal(`(ADDTIME(order_items.createdAt, waitTime)) `), 'expectedTime'],
              [Sequelize.literal(`(select address from product INNER JOIN pickup_location ON product.pickupLocationID=pickup_location.id where product.id = order_items.productID ) `), 'pickupLocation'],
              [Sequelize.literal(`(select subCategoryID from product where product.id = order_items.productID ) `), 'subCategoryID'],
            ],
            model: orderItems,
            where: [...whereClauseRefundedItem, ...whereClauseOrderItems],
            include: [
              {
                // where: [ {categoryID: [1,2] } ],
                // where: [ {categoryID: [1,2] }, ...whereClauseProduct ],
                attributes: [
                  'id',
                  'name',
                  'categoryID',
                  'subCategoryID',
                  'description',
                  'avatar',
                  'posID',
                ],
                required:false,
                model: product,
                include: [
                  {
                    attributes: [
                      'id',
                      'description',
                      'address'
                    ],
                    model: pickupLocation,
                  }
                ]
              },
              {
                attributes: [
                  'id',
                  'orderItemID',
                  'productExtrasID',
                  'price'
                ],
                model: orderItemExtras,
                include: [
                  {
                    attributes: [
                      'id',
                      'extraItem',
                      'posID',
                    ],
                    model: productExtras,
                  }
                ]
              },
              {
                attributes: [
                  'id',
                  'orderItemID',
                  'productVariantsID',
                  'price'
                ],
                model: orderItemVariants,
                include: [
                  {
                    attributes: [
                      'id',
                      'variantType'
                    ],
                    model: productVariants,
                  }
                ]
              },
              {
                attributes: [
                  ['id', "orderProductVariantTypeID"],
                  'orderItemID',
                ],
                where: Sequelize.where(Sequelize.col('`order_items`.`id`'), Sequelize.col('`order_items->order_product_variant_types`.`orderItemID`')),
                model: orderProductVariantTypes,
                required: false,
                include: [
                  {
                    attributes: [
                      ['id', "productVariantTypeID"],
                      'label',
                    ],
                    model: productVariantTypes,
                    required: true,
                  }
                ],
              }
            ]
          },
          {
            // required: true,
            model: user,
            attributes: ['id', 'fullName', 'countryCode', 'mobile', 'email']
          },
          {
            model: coupons,
            attributes: ['id', 'code', 'name', 'description']
          },
          {
            model: orderTableNumber,
            attributes: ['tableCode']
          },
          {
            attributes: [
              'id','name', 'percentage', "taxID", "amount"
            ],
            model: orderTax,
          },
          {
            attributes: [
              'id','name', 'percentage', "taxID", "amount"
            ],
            model: orderRefundTax,
          },
        ],
        order: [['createdAt', 'DESC']],
        distinct: true,
        duplicating: false,
        offset: offset,
        limit: limit,
      });

    if (orderHistoryList.count > 0) {
      for (let i = 0; i < orderHistoryList.rows.length; i++) {
        const order = orderHistoryList.rows[i];
        for (let j = 0; j < order.order_items.length; j++) {
          const item = orderHistoryList.rows[i].order_items[j];
          for (let k = 0; k < item.order_product_variant_types.length; k++) {
            const variant = orderHistoryList.rows[i].order_items[j].order_product_variant_types[k];
            let getProductSubVariant = await orderProductVariantSubTypes.find({
                attributes: [
                  ['id', "orderProductVariantSubTypeID"],
                  "orderItemID",
                  [
                    Sequelize.literal(
                      '(select variantType from product_variant_sub_types WHERE order_product_variant_sub_types.productVariantSubTypeID = `product_variant_sub_types`.id)'
                    ),
                    'extraItem'
                  ],
                  [
                    Sequelize.literal(
                      '(select price from product_variant_sub_types WHERE order_product_variant_sub_types.productVariantSubTypeID = `product_variant_sub_types`.id)'
                    ),
                    'price'
                  ],
                  [
                    Sequelize.literal(
                      '(select id from product_variant_sub_types WHERE order_product_variant_sub_types.productVariantSubTypeID = `product_variant_sub_types`.id)'
                    ),
                    'productVariantSubTypeID'
                  ],
                ],
                where: {
                  orderItemID: variant.orderItemID,
                  productVariantTypeID: variant.product_variant_type.dataValues.productVariantTypeID,
                }
            });

            orderHistoryList.rows[i].order_items[j].order_product_variant_types[k].dataValues.product_variant_type.dataValues.order_product_variant_sub_type = {
              product_variant_sub_type: {
                productVariantSubTypeID: getProductSubVariant.dataValues.productVariantSubTypeID,
                extraItem: getProductSubVariant.dataValues.extraItem,
                price: getProductSubVariant.dataValues.price,
              },
            };
            
            

          }
        }
      }
      orderHistoryList.rows = orderHistoryList && orderHistoryList.rows.map((order) => {
        let newOrderItems = groupByOrderItems(order.order_items, order.orderServiceType, order.refundStatus); // Group By order items
        // delete order.dataValues.order_items; // Delete old key
        order.dataValues['order_items_group'] = newOrderItems; // Add new key to object
        return order
      })

      res.status(200).send({
        success: 1,
        message: 'order list retrieve successfully',
        data: orderHistoryList
      })
    } else {
      res.status(200).json({
        success: 0,
        message: 'No Results Found',
        data: []
      })
    }
  } catch (error) {
    console.log(error)
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.updateDocketPrinterStatus = async (req, res) => {
  try {
    let { id, dockerPrinterStatus } = req.body

    const orderDetails = await order.findByPk(id);
    if(!orderDetails) {
      return res.status(200).json({ success: 0, message: 'Order not found.'});
    }
    console.log(orderDetails,"orderDetails1111")
    let orderUpdated = await order.update({
        docketPrintingStatus: dockerPrinterStatus
      },
      {
        where: {
          id: id
        }
      });
    
    if(orderUpdated) {
      res.json({
        success: 1,
        message: 'Order updated successfully.'
      })
    }else {
      res.json({
        success: 0,
        message: 'Order not updated.'
      })
    }
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: 'error!'
    })
  }
}

exports.addExtraTimeForPickupOrder = async (req, res) => {
  var sessionData = await jwt.verify(req.headers.accesstoken, env.ACCESS_TOKEN_SECRET);
  var userID = sessionData.userID
  try {
    let orderItemsArr = req.body.orderItemIDs.split(',');
    orderItemsArr.map(async(item) => {
      await orderItems.findOne({
        where : {
          id: item
        }
      }).then((orderItemResponse) => {
        let ReadyTime = orderItemResponse.dataValues.ReadyTime;
        ReadyTime = moment().add('00:05:00').format("YYYY-MM-DD HH:mm:ss");
        orderItemResponse.update({ ReadyTime:ReadyTime });
      })
      await orderItemWaitTimeNotifications.create({
        orderID: req.body.orderID,
        orderItemID: item,
        userID: userID,
        barID: req.body.barID,
        pickupLocationID: req.body.pickupLocationID,
        waitTime: '00:05:00',
        notification_type: '2',
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    })
    res.status(200).send({
      success: 1,
      data: {},
      message: "Extra time added successfully."
    })
  } catch (error) {
    res.status(200).send({
      success: 0,
      message: error
    })
  }
}

function sleep(ms) {
  return new Promise((resolve) => {
    setTimeout(resolve, ms);
  });
}

// exports.testAPI = async (req, res) => {
//   try {
//     let data = await commonFunction.createOrderinDoshii('QppDXWnyL', 4759, 'pickup');
//     // const chargeData = await stripe.charges.retrieve('ch_3NN97rQoWoMUiiqY0H7Cx3b1', {
//     //   expand: ['balance_transaction']
//     // }, {
//     //   stripeAccount: 'acct_1Mg4NjQoWoMUiiqY',
//     // });
//     // var totalFee = 0 ;
//     // chargeData.balance_transaction.fee_details.map((fee) => {
//     //   if(fee.type == 'stripe_fee' || fee.type == 'tax'){
//     //     totalFee = totalFee + (fee.amount / 100)
//     //   }
//     // });
//     // totalFee = totalFee.toFixed(2)
//     // const paymentIntentData = await stripe.paymentIntents.retrieve('pi_3NKyPvQoWoMUiiqY1cz8733h', {
//     //   expand: ['latest_charge.balance_transaction']
//     // }, {
//     //   stripeAccount: 'acct_1Mg4NjQoWoMUiiqY',
//     // });
//     // paymentIntentData.latest_charge.balance_transaction.fee_details.map((fee) => {
//     //   if(fee.type == 'stripe_fee' || fee.type == 'tax'){
//     //     totalFee = totalFee + (fee.amount / 100)
//     //   }
//     // });
//     // totalFee = totalFee.toFixed(2)
//     if(true) {
//       return res.json({
//         success: 1,
//         data: data,
//         message: 'Order updated successfully.',
//       })
//     }else {
//       return res.json({
//         success: 0,
//         message: 'Order not updated.'
//       })
//     }
//   } catch (error) {
//     return res.status(200).send({
//       success: 0,
//       message: error
//     })
//   }
// }


var s3 = new AWS.S3()
var uploadImage = multer({
  storage: multerS3({
    s3: s3,
    contentType: multerS3.AUTO_CONTENT_TYPE,
    bucket: function (req, file, cb) {
        cb(
          null,
          env.awsPublicBucket + '/order-receipt'
        )
    },
    key: function (req, file, cb) {
      let extArray = file.mimetype.split('/')
      let extension = extArray[extArray.length - 1]
      cb(null, Date.now().toString() + '.' + extension) //new Date().toISOString() +
    }
  }),
  limits: { fileSize: 5 * 1024 * 1024 }, // 5mb file size limit
  // bug: app couldn't send proper mime detail
  // fileFilter: fileFilter
})