// bootstrap.js
const { fetchSecretWithRetry } = require('./middleware/secretManager');
const { initDatabase } = require("./config/db");

async function bootstrap() {
    let dbConfig;
    if (process.env.AWSSECRET === 'static') {
        console.log('🛠️ Running on localhost. Fetching DB credentials from .env');   
        dbConfig = {
            host: process.env.databaseHost,
            username: process.env.databaseUserName,
            password: process.env.databasePassword,
            dbname: process.env.databaseName,
        };
    }else{
        console.log('🔐 Fetching secrets...');
        const secrets = await fetchSecretWithRetry();
        console.log('🔐 Secrets fetched successfully');

        dbConfig = {
            host: secrets.host,
            username: secrets.username,
            password: secrets.password,
            dbname: secrets.dbname,
        };
    }
    console.log('📦 Connecting to DB...');
    await initDatabase(dbConfig);
}

module.exports = bootstrap;
