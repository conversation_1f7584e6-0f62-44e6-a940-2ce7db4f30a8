var Sequelize = require('sequelize')
var subCategory = require('./subCategory')
var bar = require('./bar')
var category = require('./category')
var pickupLocation = require('./pickupLocation')
var pickupLocationSubCategory = require('./pickupLocationSubCategory')
var env = require('../config/environment')
var productVariants = require('./productVariants')
var productExtras = require('./productExtras')
const posConf = require('./POSconfig');
const productVariantSizes = require('./productVariantTypes');
const productFoodOptions = require('./productFoodOptions');
const { s3GetFile } = require('../middleware/awsS3Operations')

var product = sequelize.define(
  'product',
  {
    id: {
      type: Sequelize.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    barID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "bar",
        key: "id"
      }
    },
    categoryID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "category",
        key: "id"
      }
    },
    subCategoryID: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: "sub_category",
        key: "id"
      }
    },
    fromPosId: {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: "pos_conf",
        key: "id"
      }
    },
    name: Sequelize.TEXT,
    description: Sequelize.TEXT,
    avatar: {
      type: Sequelize.TEXT,
      get() {
        const avatarValue = this.getDataValue('avatar');
        if (avatarValue && avatarValue !== '') {
          return env.awsPublicServerURL + env.awsProductFolder + avatarValue;
        } else {
          // If product avatar is empty, use the bar avatar from the fallback field
          const barAvatarValue = this.getDataValue('barAvatar');
          if (barAvatarValue && barAvatarValue !== '') {
            return env.awsPublicServerURL + env.awsBarFolder + barAvatarValue;
          }
          return '';
        }
      }
    },
    price: Sequelize.FLOAT,
    pickupLocationID: {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: "pickup_location",
        key: "id"
      }
    },
    productTax: Sequelize.ENUM('gst', 'tax_free'),
    posID: Sequelize.STRING,
    dailyStockRenewal: Sequelize.INTEGER,
    isDailyStockRenewal: Sequelize.ENUM('Yes', 'No'),
    stock: Sequelize.INTEGER,
    isStockLimit: Sequelize.ENUM('Yes', 'No'),
    status: Sequelize.ENUM('Active', 'Inactive'),
    createdAt: Sequelize.DATE,
    updatedAt: Sequelize.DATE,
    isDeleted: Sequelize.ENUM('Yes', 'No'),
    serviceType: Sequelize.ENUM('PICKUP', 'TABLE', 'BOTH'),
    isUpdateByUser: Sequelize.ENUM('Yes', 'No'),
  },
  {
    freezeTableName: true,
    timestamps: false
  }
)

product.belongsTo(category, { foreignKey: 'categoryID' })
product.belongsTo(subCategory, { foreignKey: 'subCategoryID' })
product.belongsTo(pickupLocation, { foreignKey: 'pickupLocationID' })
product.belongsTo(posConf, { foreignKey: 'fromPosId' })
product.belongsTo(bar, { foreignKey: 'barID' })

bar.hasMany(product, { foreignKey: 'barID' })
product.hasMany(productVariants, { foreignKey: 'productID', as: 'product_variants' })
product.hasMany(productExtras, { foreignKey: 'productID', as: 'product_extras' })
product.hasMany(productVariantSizes, { foreignKey: 'productID', as: 'productVariantTypes' })
product.hasMany(productFoodOptions, { foreignKey: 'productID', as: 'productFoodOptions' })
pickupLocationSubCategory.hasMany(product, { foreignKey: 'subCategoryID', sourceKey: 'subCategoryID' })

productExtras.belongsTo(product, { foreignKey: 'productID' })
productVariants.belongsTo(product, { foreignKey: 'productID' })

module.exports = product