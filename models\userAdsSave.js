var Sequelize = require('sequelize')
var env = require('../config/environment')

var userAdsSave = sequelize.define(
    'user_ads_save',
    {
        id: {
            type: Sequelize.INTEGER,
            autoIncrement: true,
            primaryKey: true
        },

        adsID: {
            type: Sequelize.INTEGER,
            allowNull: false
        },
        userID: {
            type: Sequelize.INTEGER,
            allowNull: false,            
        },        
        created_at: {
            type: Sequelize.DATE,
            allowNull: true,
            defaultValue: Sequelize.NOW
        },
        updated_at: {
            type: Sequelize.DATE,
            allowNull: true,
            defaultValue: Sequelize.NOW
        }        
    },
    {
        freezeTableName: true,
        timestamps: false
    }
)
module.exports = userAdsSave