var Sequelize = require('sequelize')
const message = require('../config/message')
const page = require('../models/page')
const crashLogsModel = require('../models/crashLogs')
const Op = Sequelize.Op
const literal = Sequelize.literal
const commonFunction = require('../common/commonFunction')
var env = require('../config/environment')
var jwt = require('jsonwebtoken');
const crypto = require('crypto');

exports.privacyPolicy = (req, res) => {
  page
    .findOne({
      attributes: ['title', 'content'],
      where: {
        id: 1
      }
    })
    .then(resData => {
      res.status(200).json({
        success: 1,
        message: 'Success',
        data: resData
      })
    })
}

exports.faq = (req, res) => {
  page
    .findOne({
      attributes: ['title', 'content'],
      where: {
        id: 2
      }
    })
    .then(resData => {
      res.status(200).json({
        success: 1,
        message: 'Success',
        data: resData
      })
    })
}

exports.terms = (req, res) => {
  page
    .findOne({
      attributes: ['title', 'content'],
      where: {
        id: 3
      }
    })
    .then(resData => {
      res.status(200).json({
        success: 1,
        message: 'Success',
        data: resData
      })
    })
}

exports.barAboutUs = (req, res) => {
  page
    .findOne({
      attributes: ['title', 'content'],
      where: {
        id: 4
      }
    })
    .then(resData => {
      res.status(200).json({
        success: 1,
        message: 'Success',
        data: resData
      })
    })
}

exports.barPrivacyPolicy = (req, res) => {
  page
    .findOne({
      attributes: ['title', 'content'],
      where: {
        id: 5
      }
    })
    .then(resData => {
      res.status(200).json({
        success: 1,
        message: 'Success',
        data: resData
      })
    })
}

exports.barFaq = (req, res) => {
  page
    .findOne({
      attributes: ['title', 'content'],
      where: {
        id: 6
      }
    })
    .then(resData => {
      res.status(200).json({
        success: 1,
        message: 'Success',
        data: resData
      })
    })
}

exports.barTerms = (req, res) => {
  page
    .findOne({
      attributes: ['title', 'content'],
      where: {
        id: 7
      }
    })
    .then(resData => {
      res.status(200).json({
        success: 1,
        message: 'Success',
        data: resData
      })
    })
}
exports.crashLogs = async (req, res) => {
  try {
    const payload = req.body;
    if (!payload || typeof payload !== 'object') {
      return res.status(400).json({ error: 'Invalid JSON payload' });
    }
    const record = await crashLogsModel.create({ payload });
    res.status(201).json({
      message: 'Crash log saved',
      id: record.id
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};
exports.getcrashLogs = async (req, res) => {
  try {
    console.log('getcrashLogs...')
    console.log(req.body);
    const { start, end } = req.body;
    if (!start || !end) {
      return res.status(400).json({ error: 'Start and end dates are required (YYYY-MM-DD)' });
    }
    const logs = await crashLogsModel.findAll({
      where: literal(`DATE(created_at) BETWEEN '${start}' AND '${end}'`),
      order: [['created_at', 'DESC']]
    }); res.status(200).json(logs);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};
exports.facebookDeletion = async (req, res) => {
  console.log('facebookDeletion...');
  
  try {
    const signedRequest = req.body.signed_request;
    const APP_SECRET = env.facebook_app_secret;
    
    if (!signedRequest) {
      return res.status(400).json({ error: 'Missing signed_request' });
    }

    const [encodedSig, payload] = signedRequest.split('.');
    if (!encodedSig || !payload) {
      return res.status(400).json({ error: 'Invalid signed_request format' });
    }
    console.log('Processing signed request...');
    const sig = commonFunction.base64UrlDecode(encodedSig);
    console.log('sig=>', sig);

    // Decode the payload and check what we get
    const decodedPayload = commonFunction.base64UrlDecode(payload).toString();
    console.log('Decoded payload string:', decodedPayload);

    // Check if the decoded payload looks like JSON
    if (!decodedPayload.startsWith('{') && !decodedPayload.startsWith('[')) {
      console.error('Decoded payload is not JSON format:', decodedPayload);
      return res.status(400).json({
        error: 'Invalid payload format - not JSON',
        decoded: decodedPayload
      });
    }

    let data;
    try {
      data = JSON.parse(decodedPayload);
    } catch (parseError) {
      console.error('JSON parse error:', parseError.message);
      console.error('Payload that failed to parse:', decodedPayload);
      return res.status(400).json({
        error: 'Invalid JSON in payload',
        payload: decodedPayload,
        parseError: parseError.message
      });
    }

    const expectedSig = crypto
      .createHmac('sha256', APP_SECRET)
      .update(payload)
      .digest();

    // Verify the signature
    if (!crypto.timingSafeEqual(sig, expectedSig)) {
      console.error('Invalid signature!');
      return res.status(400).json({ error: 'Invalid signature' });
    }

    const userId = data.user_id;
    console.log('facebook deletion userID==', userId);
    
    // TODO: Actually delete user data here
    // await deleteUserData(userId);
    
    const confirmationCode = `${userId}_${Date.now()}`; // More unique confirmation code
    
    return res.status(200)
      .set('Content-Type', 'application/json')
      .json({
        url: 'https://mytabinfo.com/policies/privacy-policy',
        confirmation_code: confirmationCode
      });
      
  } catch (error) {
    console.error('Error in facebookDeletion:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
};
