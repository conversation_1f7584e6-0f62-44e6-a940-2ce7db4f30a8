var Sequelize = require('sequelize')
var user = require('./user')
var bar = require('./bar')
var segment = require('./segment')
var ads = require('./ads')

var env = require('../config/environment')

var advertiser_user = sequelize.define(
    'advertiser_user',
    {
        id: {
            type: Sequelize.BIGINT,
            autoIncrement: true,
            primaryKey: true
        },
        profile_image: {
            type: Sequelize.STRING(555),
            get() {
                if (
                    this.getDataValue('profile_image') != '' &&
                    this.getDataValue('profile_image') != null
                ) {
                    return (
                        env.awsPrivateBucketCloudFrontURL +
                        env.awsAdsFolder +
                        this.getDataValue('profile_image')
                    );
                } else {
                    return '';
                }
            },
            defaultValue: null
        },
        business_name: Sequelize.STRING(255),
        business_url: Sequelize.STRING(255),
        acn_number: Sequelize.STRING(255),
        contact_name: Sequelize.STRING(255),
        email: Sequelize.STRING(255),
        password: Sequelize.STRING(255),
        password_updated_at: Sequelize.DATE,
        mfa_code: Sequelize.STRING(200),
        mfa_qr_code: {
            type: Sequelize.STRING(200),
            get() {
                if (
                    this.getDataValue('mfa_qr_code') != '' &&
                    this.getDataValue('mfa_qr_code') != null
                ) {
                    return (
                        env.awsPrivateBucketCloudFrontURL +
                        env.awsAdsFolder +
                        this.getDataValue('mfa_qr_code')
                    );
                } else {
                    return '';
                }
            }
        },
        notification: {
            type: Sequelize.ENUM('Yes', 'No'),
            defaultValue: 'Yes'
        },
        account_verified: {
            type: Sequelize.ENUM('New', 'Approved', 'Rejected'),
            defaultValue: 'New'
        },
        status: {
            type: Sequelize.ENUM('Active', 'Inactive'),
            defaultValue: 'Active'
        },
        timezone: Sequelize.INTEGER,
        timezone_value: Sequelize.STRING(255),
        otp_token: Sequelize.STRING(555)
    },
    {
        freezeTableName: true,
        timestamps: false
    }
)

module.exports = advertiser_user