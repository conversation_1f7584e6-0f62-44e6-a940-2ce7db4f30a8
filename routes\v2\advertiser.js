const express = require('express')
const router = express()
const multer = require('multer')

const advertiser = require('../../controller/v2/advertiser.js')
const checkUserAuth = require('../../middleware/checkAuth')

var upload = multer({})

router.post('/advertiserList', upload.array(), checkUserAuth, advertiser.advertiserList)
router.post('/saveUserAds', upload.array(), checkUserAuth, advertiser.saveUserAds)
router.post('/userAdsClick', upload.array(), checkUserAuth, advertiser.userAdsClick)
router.post('/userAdsImpression', upload.array(), checkUserAuth, advertiser.userAdsImpression)

module.exports = router